#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Optimized ASCE with improved connectivity and learning."""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

def create_optimized_model():
    """Create an optimized ASCE model with better connectivity."""
    print("=" * 60)
    print("OPTIMIZED ASCE MODEL")
    print("=" * 60)
    
    try:
        from training.llm_trainer import ASCELanguageModel, TrainingConfig
        
        # Optimized configuration
        config = TrainingConfig(
            vocab_size=200,
            sequence_length=24,
            embedding_dim=64,
            num_layers=3,
            batch_size=2,
            learning_rate=0.01,  # Higher learning rate
            max_steps=50,
            save_interval=25,
            eval_interval=15
        )
        
        print("Creating Optimized ASCE Model...")
        model = ASCELanguageModel(config)
        
        # Enhanced dataset with more structure
        texts = [
            # Simple patterns
            "cat sits mat", "dog runs park", "bird flies sky",
            "sun shines bright", "rain falls down", "wind blows strong",
            
            # AI patterns
            "ai learns data", "neural network processes", "machine learning improves",
            "algorithm solves problems", "computer calculates fast", "robot helps humans",
            
            # Action patterns
            "student reads book", "teacher explains concept", "child plays game",
            "scientist discovers truth", "artist creates beauty", "engineer builds bridge",
            
            # Simple conversations
            "hello friend", "good morning", "thank you", "please help",
            "see you later", "have nice day", "welcome home", "goodbye now"
        ]
        
        print(f"Dataset: {len(texts)} structured texts")
        
        # Build vocabulary
        model.data_processor.build_vocabulary(texts)
        vocab_size = len(model.data_processor.vocab)
        print(f"Vocabulary: {vocab_size} tokens")
        
        # Manual connection enhancement
        print("Enhancing network connectivity...")
        for cpu in model.cpu_layers:
            # Add more connections between layers
            input_ids = list(cpu.input_neuron_ids)
            output_ids = list(cpu.output_neuron_ids)
            
            # Connect input to output with higher probability
            for i, input_id in enumerate(input_ids[:10]):  # Limit to avoid too many
                for j, output_id in enumerate(output_ids[:5]):
                    if np.random.rand() < 0.5:  # 50% connection probability
                        cpu.network.add_connection(input_id, output_id, weight=0.3)
            
            print(f"  CPU {cpu.id}: Enhanced connectivity")
        
        return model, texts
        
    except Exception as e:
        print(f"Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def train_optimized_model(model, texts):
    """Train the optimized model with better learning."""
    print("\nOptimized Training:")
    print("-" * 40)
    
    # Split data
    train_texts = texts[:int(len(texts) * 0.8)]
    eval_texts = texts[int(len(texts) * 0.8):]
    
    # Training with enhanced monitoring
    losses = []
    best_loss = float('inf')
    
    for step in range(50):
        # Create batch
        batch_texts = np.random.choice(train_texts, size=2, replace=True)
        
        # Manual batch creation for better control
        batch_inputs = []
        batch_targets = []
        
        for text in batch_texts:
            encoded = model.data_processor.encode_text(text, max_length=25)
            batch_inputs.append(encoded[:-1])  # Input
            batch_targets.append(encoded[1:])   # Target (shifted)
        
        inputs = np.array(batch_inputs)
        targets = np.array(batch_targets)
        
        # Training step
        step_result = model.train_step(inputs, targets)
        loss = step_result["loss"]
        losses.append(loss)
        
        # Enhanced logging
        if step % 5 == 0:
            avg_loss = np.mean(losses[-5:]) if len(losses) >= 5 else loss
            lr = step_result["learning_rate"]
            curiosity = model.ms.get_curiosity_signal()
            
            print(f"Step {step:2d}: Loss={avg_loss:.4f}, LR={lr:.6f}, Curiosity={curiosity:.3f}")
            
            # Test generation every 10 steps
            if step % 10 == 0:
                test_prompt = "cat"
                generated = model.generate_text(test_prompt, max_length=4, temperature=0.8)
                print(f"        Test: '{test_prompt}' -> '{generated}'")
        
        # Save best model
        if loss < best_loss:
            best_loss = loss
    
    print(f"\nTraining completed. Best loss: {best_loss:.4f}")
    return best_loss

def test_optimized_generation(model):
    """Test the optimized model generation."""
    print("\n" + "=" * 40)
    print("OPTIMIZED GENERATION TEST")
    print("=" * 40)
    
    test_cases = [
        # Simple completions
        ("cat", 4),
        ("ai", 3),
        ("hello", 3),
        ("student", 4),
        
        # Longer completions
        ("neural network", 5),
        ("good morning", 4),
        ("machine learning", 5)
    ]
    
    print("Generation Results:")
    for prompt, max_len in test_cases:
        # Test with different temperatures
        for temp in [0.5, 1.0, 1.5]:
            try:
                generated = model.generate_text(prompt, max_length=max_len, temperature=temp)
                print(f"  '{prompt}' (T={temp}) -> '{generated}'")
            except Exception as e:
                print(f"  '{prompt}' (T={temp}) -> ERROR: {e}")

def analyze_network_state(model):
    """Analyze the current network state."""
    print("\n" + "=" * 40)
    print("NETWORK STATE ANALYSIS")
    print("=" * 40)
    
    # CPU analysis
    print("CPU Layers:")
    total_neurons = 0
    total_connections = 0
    
    for i, cpu in enumerate(model.cpu_layers):
        stats = cpu.get_cpu_statistics()
        neurons = stats['network_stats']['total_neurons']
        connections = stats['network_stats']['total_connections']
        activity = stats['network_stats']['current_activity']
        sparsity = stats['network_stats']['network_sparsity']
        
        total_neurons += neurons
        total_connections += connections
        
        print(f"  Layer {i}: {neurons} neurons, {connections} connections")
        print(f"           Activity: {activity:.3f}, Sparsity: {sparsity:.3f}")
    
    print(f"  Total: {total_neurons} neurons, {total_connections} connections")
    
    # Memory analysis
    ams_stats = model.ams.get_memory_statistics()
    print(f"\nMemory System:")
    print(f"  Traces: {ams_stats['total_traces']}")
    print(f"  Types: {ams_stats['type_distribution']}")
    print(f"  Consolidation: {ams_stats['consolidation_distribution']}")
    
    # Modulatory analysis
    ms_stats = model.ms.get_system_statistics()
    print(f"\nModulatory System:")
    print(f"  State: {ms_stats['motivation']['state']}")
    print(f"  Curiosity: {ms_stats['motivation']['intrinsic_motivation']:.3f}")
    print(f"  Attention: {ms_stats['attention']['mode']}")
    print(f"  Energy: {ms_stats['homeostasis']['energy_level']:.3f}")
    
    # Neuromodulator analysis
    neuromod = model.ms.get_neuromodulator_levels()
    print(f"\nNeuromodulators:")
    for nt, level in neuromod.items():
        status = "HIGH" if level > 0.7 else "LOW" if level < 0.3 else "NORMAL"
        print(f"  {nt.capitalize()}: {level:.3f} ({status})")

def run_interactive_session(model):
    """Run an interactive session with the optimized model."""
    print("\n" + "=" * 40)
    print("INTERACTIVE SESSION")
    print("=" * 40)
    print("Enter prompts (type 'quit' to exit):")
    
    while True:
        try:
            prompt = input("\nPrompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                break
            
            if not prompt:
                continue
            
            # Generate with default settings
            generated = model.generate_text(prompt, max_length=6, temperature=0.8)
            print(f"Generated: {generated}")
            
            # Show system state
            curiosity = model.ms.get_curiosity_signal()
            learning_rate = model.ms.get_learning_modulation()
            print(f"System: Curiosity={curiosity:.3f}, LR={learning_rate:.6f}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("Interactive session ended.")

def main():
    """Main function for optimized ASCE testing."""
    print("ASCE OPTIMIZATION AND TESTING")
    print("=" * 60)
    
    # Create optimized model
    model, texts = create_optimized_model()
    
    if model is None:
        print("Failed to create model")
        return
    
    # Train optimized model
    best_loss = train_optimized_model(model, texts)
    
    # Test generation
    test_optimized_generation(model)
    
    # Analyze network state
    analyze_network_state(model)
    
    # Interactive session
    print("\nStarting interactive session...")
    run_interactive_session(model)
    
    print("\nOptimization testing completed!")

if __name__ == "__main__":
    main()
