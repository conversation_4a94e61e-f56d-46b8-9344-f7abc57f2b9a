# -*- coding: utf-8 -*-

"""Advanced LLM Training System for ASCE - Language Model Capabilities."""

import numpy as np
import random
import json
import pickle
import os
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, deque
from dataclasses import dataclass

from core.sparse_network import AdvancedSparseNetwork
from core.plasticity import AdvancedPlasticityEngine
from components.cpu import AdvancedCPU
from components.ams import AdvancedAMS, MemoryType
from components.ms import AdvancedModulatorySystem

@dataclass
class TrainingConfig:
    """Configuration for LLM training."""
    vocab_size: int = 50000
    sequence_length: int = 512
    embedding_dim: int = 768
    num_layers: int = 12
    attention_heads: int = 12
    batch_size: int = 32
    learning_rate: float = 0.0001
    warmup_steps: int = 4000
    max_steps: int = 100000
    save_interval: int = 1000
    eval_interval: int = 500

class TokenEmbedding:
    """Advanced token embedding system for ASCE."""
    
    def __init__(self, vocab_size: int, embedding_dim: int):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        
        # Sparse embedding matrix
        self.embeddings = np.random.normal(0, 0.02, (vocab_size, embedding_dim))
        
        # Token frequency tracking
        self.token_frequencies = defaultdict(int)
        self.total_tokens = 0
        
        # Positional embeddings
        self.max_position = 2048
        self.position_embeddings = self._create_positional_embeddings()
    
    def _create_positional_embeddings(self) -> np.ndarray:
        """Create sinusoidal positional embeddings."""
        pos_enc = np.zeros((self.max_position, self.embedding_dim))
        
        for pos in range(self.max_position):
            for i in range(0, self.embedding_dim, 2):
                pos_enc[pos, i] = np.sin(pos / (10000 ** (i / self.embedding_dim)))
                if i + 1 < self.embedding_dim:
                    pos_enc[pos, i + 1] = np.cos(pos / (10000 ** (i / self.embedding_dim)))
        
        return pos_enc
    
    def encode_tokens(self, tokens: List[int], positions: List[int] = None) -> np.ndarray:
        """Encode tokens with positional information."""
        if positions is None:
            positions = list(range(len(tokens)))
        
        # Get token embeddings
        token_embeds = self.embeddings[tokens]
        
        # Add positional embeddings
        pos_embeds = self.position_embeddings[positions]
        
        # Combine embeddings
        combined = token_embeds + pos_embeds
        
        # Update frequency tracking
        for token in tokens:
            self.token_frequencies[token] += 1
            self.total_tokens += 1
        
        return combined

class LanguageDataProcessor:
    """Process text data for ASCE training."""
    
    def __init__(self, vocab_size: int = 50000):
        self.vocab_size = vocab_size
        self.vocab = {}  # {token: id}
        self.reverse_vocab = {}  # {id: token}
        self.special_tokens = {
            "<PAD>": 0,
            "<UNK>": 1,
            "<BOS>": 2,
            "<EOS>": 3,
            "<MASK>": 4
        }
        
        # Initialize with special tokens
        for token, token_id in self.special_tokens.items():
            self.vocab[token] = token_id
            self.reverse_vocab[token_id] = token
        
        self.next_id = len(self.special_tokens)
        
        # Text processing
        self.token_counts = defaultdict(int)
        self.processed_sequences = []
    
    def build_vocabulary(self, texts: List[str]):
        """Build vocabulary from text corpus."""
        print("Building vocabulary...")
        
        # Tokenize and count
        for text in texts:
            tokens = self._simple_tokenize(text)
            for token in tokens:
                self.token_counts[token] += 1
        
        # Select most frequent tokens
        sorted_tokens = sorted(self.token_counts.items(), key=lambda x: x[1], reverse=True)
        
        for token, count in sorted_tokens[:self.vocab_size - len(self.special_tokens)]:
            if token not in self.vocab:
                self.vocab[token] = self.next_id
                self.reverse_vocab[self.next_id] = token
                self.next_id += 1
        
        print(f"Vocabulary built with {len(self.vocab)} tokens")
    
    def _simple_tokenize(self, text: str) -> List[str]:
        """Simple tokenization (can be replaced with more sophisticated methods)."""
        # Basic word-level tokenization
        import re
        tokens = re.findall(r'\w+|[^\w\s]', text.lower())
        return tokens
    
    def encode_text(self, text: str, max_length: int = 512) -> List[int]:
        """Encode text to token IDs."""
        tokens = self._simple_tokenize(text)
        
        # Convert to IDs
        token_ids = []
        token_ids.append(self.special_tokens["<BOS>"])
        
        for token in tokens:
            if token in self.vocab:
                token_ids.append(self.vocab[token])
            else:
                token_ids.append(self.special_tokens["<UNK>"])
        
        token_ids.append(self.special_tokens["<EOS>"])
        
        # Truncate or pad
        if len(token_ids) > max_length:
            token_ids = token_ids[:max_length]
        else:
            token_ids.extend([self.special_tokens["<PAD>"]] * (max_length - len(token_ids)))
        
        return token_ids
    
    def decode_tokens(self, token_ids: List[int]) -> str:
        """Decode token IDs back to text."""
        tokens = []
        for token_id in token_ids:
            if token_id in self.reverse_vocab:
                token = self.reverse_vocab[token_id]
                if token not in ["<PAD>", "<BOS>", "<EOS>"]:
                    tokens.append(token)
        
        return " ".join(tokens)
    
    def create_training_batches(self, texts: List[str], batch_size: int, 
                               sequence_length: int) -> List[Tuple[np.ndarray, np.ndarray]]:
        """Create training batches with input-target pairs."""
        batches = []
        
        # Encode all texts
        encoded_texts = []
        for text in texts:
            encoded = self.encode_text(text, sequence_length + 1)
            encoded_texts.append(encoded)
        
        # Create batches
        for i in range(0, len(encoded_texts), batch_size):
            batch_texts = encoded_texts[i:i + batch_size]
            
            # Pad batch to same size
            while len(batch_texts) < batch_size:
                batch_texts.append([self.special_tokens["<PAD>"]] * (sequence_length + 1))
            
            # Create input-target pairs
            inputs = np.array([seq[:-1] for seq in batch_texts])
            targets = np.array([seq[1:] for seq in batch_texts])
            
            batches.append((inputs, targets))
        
        return batches

class ASCELanguageModel:
    """ASCE-based Language Model with advanced neural architecture."""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        
        # Initialize components
        self.embedding = TokenEmbedding(config.vocab_size, config.embedding_dim)
        self.data_processor = LanguageDataProcessor(config.vocab_size)
        
        # Create ASCE architecture for language modeling
        self._build_language_architecture()
        
        # Training state
        self.step = 0
        self.epoch = 0
        self.best_loss = float('inf')
        self.training_history = []
    
    def _build_language_architecture(self):
        """Build ASCE architecture optimized for language modeling."""
        # Modulatory System
        self.ms = AdvancedModulatorySystem(ms_id=0, context_dimensions=128)
        
        # Abstract Memory System for language patterns
        self.ams = AdvancedAMS(
            ams_id=0,
            representation_size=self.config.embedding_dim,
            max_traces=50000
        )
        
        # Multiple CPU layers for hierarchical processing
        self.cpu_layers = []
        
        # Input layer CPU
        input_config = {
            "input_size": self.config.embedding_dim,
            "output_size": self.config.embedding_dim,
            "hidden_layers": [self.config.embedding_dim * 2, self.config.embedding_dim],
            "working_memory_size": 64,
            "attention_size": 32,
            "sparsity_level": 0.1,
            "recurrent_connections": True,
            "attention_mechanism": True
        }
        
        for layer_idx in range(self.config.num_layers):
            cpu = AdvancedCPU(
                cpu_id=layer_idx,
                architecture_config=input_config,
                plasticity_rule="advanced"
            )
            self.cpu_layers.append(cpu)
        
        # Output projection layer
        self.output_projection = np.random.normal(
            0, 0.02, (self.config.embedding_dim, self.config.vocab_size)
        )
        
        print(f"Built ASCE Language Model with {self.config.num_layers} layers")
    
    def forward(self, input_tokens: np.ndarray, training: bool = True) -> np.ndarray:
        """Forward pass through the ASCE language model."""
        batch_size, seq_length = input_tokens.shape
        
        # Embed tokens
        embedded_inputs = []
        for i in range(batch_size):
            positions = list(range(seq_length))
            embedded = self.embedding.encode_tokens(input_tokens[i].tolist(), positions)
            embedded_inputs.append(embedded)
        
        embedded_inputs = np.array(embedded_inputs)
        
        # Process through CPU layers
        layer_outputs = []
        current_input = embedded_inputs
        
        for layer_idx, cpu in enumerate(self.cpu_layers):
            layer_output = self._process_layer(cpu, current_input, layer_idx, training)
            layer_outputs.append(layer_output)
            current_input = layer_output
        
        # Final output projection
        final_output = current_input
        logits = np.dot(final_output, self.output_projection)
        
        return logits
    
    def _process_layer(self, cpu: AdvancedCPU, inputs: np.ndarray, 
                      layer_idx: int, training: bool) -> np.ndarray:
        """Process inputs through a single CPU layer."""
        batch_size, seq_length, embed_dim = inputs.shape
        outputs = np.zeros_like(inputs)
        
        # Get modulatory signals
        modulatory_signals = self.ms.get_neuromodulator_levels()
        
        for batch_idx in range(batch_size):
            for seq_idx in range(seq_length):
                # Apply input to CPU
                input_vector = inputs[batch_idx, seq_idx]
                cpu.apply_input(input_vector, input_type="vector")
                
                # Step CPU with context
                context_info = {
                    "layer_idx": layer_idx,
                    "sequence_position": seq_idx,
                    "batch_idx": batch_idx
                }
                
                step_result = cpu.step(
                    modulatory_signals=modulatory_signals,
                    context_info=context_info,
                    processing_mode="recurrent" if training else "feedforward"
                )
                
                # Get output
                output_vector = cpu.get_output(output_format="vector")
                outputs[batch_idx, seq_idx] = output_vector
        
        return outputs

    def calculate_loss(self, logits: np.ndarray, targets: np.ndarray) -> float:
        """Calculate cross-entropy loss."""
        batch_size, seq_length, vocab_size = logits.shape

        # Reshape for loss calculation
        logits_flat = logits.reshape(-1, vocab_size)
        targets_flat = targets.reshape(-1)

        # Softmax
        exp_logits = np.exp(logits_flat - np.max(logits_flat, axis=1, keepdims=True))
        probs = exp_logits / np.sum(exp_logits, axis=1, keepdims=True)

        # Cross-entropy loss
        loss = 0.0
        valid_tokens = 0

        for i in range(len(targets_flat)):
            target = targets_flat[i]
            if target != self.data_processor.special_tokens["<PAD>"]:
                loss -= np.log(probs[i, target] + 1e-8)
                valid_tokens += 1

        return loss / max(valid_tokens, 1)

    def train_step(self, inputs: np.ndarray, targets: np.ndarray) -> Dict[str, float]:
        """Perform one training step."""
        # Forward pass
        logits = self.forward(inputs, training=True)

        # Calculate loss
        loss = self.calculate_loss(logits, targets)

        # Apply ASCE learning mechanisms
        self._apply_asce_learning(inputs, targets, logits, loss)

        # Update modulatory system
        self._update_modulatory_system(loss)

        # Store patterns in AMS
        self._store_language_patterns(inputs, targets)

        self.step += 1

        return {
            "loss": loss,
            "step": self.step,
            "learning_rate": self.ms.get_learning_modulation()
        }

    def _apply_asce_learning(self, inputs: np.ndarray, targets: np.ndarray,
                            logits: np.ndarray, loss: float):
        """Apply ASCE-specific learning mechanisms."""
        # Calculate prediction errors for each position
        batch_size, seq_length = inputs.shape

        for layer_idx, cpu in enumerate(self.cpu_layers):
            # Calculate layer-specific prediction error
            layer_error = loss * (1.0 / len(self.cpu_layers))

            # Apply plasticity with error-driven modulation
            modulatory_signals = {
                "reward_prediction_error": layer_error,
                "uncertainty": min(1.0, loss),
                "arousal": min(1.0, loss * 2.0)
            }

            # Context for this layer
            context_info = {
                "layer_idx": layer_idx,
                "prediction_error": layer_error,
                "loss": loss
            }

            # Apply advanced plasticity
            plasticity_stats = cpu.plasticity_engine.apply_advanced_edsp(
                network=cpu.network,
                modulatory_signals=modulatory_signals,
                context_info=context_info
            )

    def _update_modulatory_system(self, loss: float):
        """Update modulatory system based on training progress."""
        # Calculate novelty based on loss
        novelty = min(1.0, loss)

        # Calculate success based on loss improvement
        if len(self.training_history) > 0:
            last_loss = self.training_history[-1]["loss"]
            success = 1.0 if loss < last_loss else 0.0
        else:
            success = 0.5

        # System feedback
        system_feedback = {
            "prediction_error": loss,
            "task_success": success,
            "novelty_level": novelty,
            "uncertainty": min(1.0, loss),
            "efficiency": max(0.0, 1.0 - loss)
        }

        # External signals
        external_signals = {
            "reward": 1.0 - loss,
            "stress": min(1.0, loss * 2.0)
        }

        # Update MS
        self.ms.update_state(system_feedback, external_signals)

    def _store_language_patterns(self, inputs: np.ndarray, targets: np.ndarray):
        """Store language patterns in AMS."""
        batch_size, seq_length = inputs.shape

        for batch_idx in range(min(batch_size, 4)):  # Store only a few per batch
            for seq_idx in range(0, seq_length, 8):  # Sample every 8th position
                # Create pattern from input context
                context_start = max(0, seq_idx - 4)
                context_end = min(seq_length, seq_idx + 4)

                context_tokens = inputs[batch_idx, context_start:context_end]
                target_token = targets[batch_idx, seq_idx] if seq_idx < seq_length else 0

                # Embed context
                context_embedded = self.embedding.encode_tokens(
                    context_tokens.tolist(),
                    list(range(len(context_tokens)))
                )

                # Average embedding as pattern
                pattern = np.mean(context_embedded, axis=0)

                # Store in AMS
                context_info = {
                    "sequence_position": seq_idx,
                    "target_token": int(target_token),
                    "context_length": len(context_tokens)
                }

                self.ams.store_pattern(
                    pattern=pattern,
                    memory_type=MemoryType.SEMANTIC,
                    context=context_info,
                    importance=0.5
                )

    def generate_text(self, prompt: str, max_length: int = 100,
                     temperature: float = 1.0) -> str:
        """Generate text using the trained model."""
        # Encode prompt
        prompt_tokens = self.data_processor.encode_text(prompt, max_length=50)

        # Remove padding and special tokens for generation
        prompt_tokens = [t for t in prompt_tokens
                        if t not in [self.data_processor.special_tokens["<PAD>"],
                                   self.data_processor.special_tokens["<EOS>"]]]

        generated_tokens = prompt_tokens.copy()

        for _ in range(max_length):
            # Prepare input (last sequence_length tokens)
            input_seq = generated_tokens[-self.config.sequence_length:]
            if len(input_seq) < self.config.sequence_length:
                # Pad at the beginning
                padding = [self.data_processor.special_tokens["<PAD>"]] * (
                    self.config.sequence_length - len(input_seq))
                input_seq = padding + input_seq

            # Forward pass
            input_batch = np.array([input_seq])
            logits = self.forward(input_batch, training=False)

            # Get logits for last position
            last_logits = logits[0, -1, :]

            # Apply temperature
            if temperature > 0:
                last_logits = last_logits / temperature

            # Sample next token
            exp_logits = np.exp(last_logits - np.max(last_logits))
            probs = exp_logits / np.sum(exp_logits)

            # Sample from distribution
            next_token = np.random.choice(len(probs), p=probs)

            # Stop if EOS token
            if next_token == self.data_processor.special_tokens["<EOS>"]:
                break

            generated_tokens.append(next_token)

        # Decode to text
        return self.data_processor.decode_tokens(generated_tokens)

    def evaluate(self, eval_texts: List[str]) -> Dict[str, float]:
        """Evaluate model on validation data."""
        total_loss = 0.0
        total_batches = 0

        eval_batches = self.data_processor.create_training_batches(
            eval_texts, self.config.batch_size, self.config.sequence_length
        )

        for inputs, targets in eval_batches[:10]:  # Evaluate on subset
            logits = self.forward(inputs, training=False)
            loss = self.calculate_loss(logits, targets)
            total_loss += loss
            total_batches += 1

        avg_loss = total_loss / max(total_batches, 1)
        perplexity = np.exp(avg_loss)

        return {
            "eval_loss": avg_loss,
            "perplexity": perplexity
        }

    def save_model(self, filepath: str):
        """Save model state."""
        model_state = {
            "config": self.config.__dict__,
            "step": self.step,
            "epoch": self.epoch,
            "best_loss": self.best_loss,
            "embedding": {
                "embeddings": self.embedding.embeddings,
                "position_embeddings": self.embedding.position_embeddings,
                "token_frequencies": dict(self.embedding.token_frequencies)
            },
            "data_processor": {
                "vocab": self.data_processor.vocab,
                "reverse_vocab": self.data_processor.reverse_vocab,
                "token_counts": dict(self.data_processor.token_counts)
            },
            "output_projection": self.output_projection,
            "ms_state": self.ms.save_state(),
            "ams_state": self.ams.save_state(),
            "cpu_states": [cpu.save_state() for cpu in self.cpu_layers],
            "training_history": self.training_history
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_state, f)

        print(f"Model saved to {filepath}")

    def load_model(self, filepath: str):
        """Load model state."""
        with open(filepath, 'rb') as f:
            model_state = pickle.load(f)

        # Restore state
        self.step = model_state["step"]
        self.epoch = model_state["epoch"]
        self.best_loss = model_state["best_loss"]
        self.training_history = model_state["training_history"]

        # Restore embedding
        self.embedding.embeddings = model_state["embedding"]["embeddings"]
        self.embedding.position_embeddings = model_state["embedding"]["position_embeddings"]
        self.embedding.token_frequencies = defaultdict(int, model_state["embedding"]["token_frequencies"])

        # Restore data processor
        self.data_processor.vocab = model_state["data_processor"]["vocab"]
        self.data_processor.reverse_vocab = model_state["data_processor"]["reverse_vocab"]
        self.data_processor.token_counts = defaultdict(int, model_state["data_processor"]["token_counts"])

        # Restore output projection
        self.output_projection = model_state["output_projection"]

        print(f"Model loaded from {filepath}")

    def train(self, train_texts: List[str], eval_texts: List[str] = None,
             save_dir: str = "checkpoints"):
        """Train the ASCE language model."""
        print("Starting ASCE Language Model Training...")

        # Create save directory
        os.makedirs(save_dir, exist_ok=True)

        # Build vocabulary
        self.data_processor.build_vocabulary(train_texts)

        # Create training batches
        train_batches = self.data_processor.create_training_batches(
            train_texts, self.config.batch_size, self.config.sequence_length
        )

        print(f"Created {len(train_batches)} training batches")

        # Training loop
        for epoch in range(100):  # Max epochs
            self.epoch = epoch
            epoch_loss = 0.0

            # Shuffle batches
            random.shuffle(train_batches)

            for batch_idx, (inputs, targets) in enumerate(train_batches):
                # Training step
                step_result = self.train_step(inputs, targets)
                epoch_loss += step_result["loss"]

                # Log progress
                if self.step % 100 == 0:
                    avg_loss = epoch_loss / (batch_idx + 1)
                    print(f"Epoch {epoch}, Step {self.step}, Loss: {avg_loss:.4f}, "
                          f"LR: {step_result['learning_rate']:.6f}")

                # Evaluation
                if eval_texts and self.step % self.config.eval_interval == 0:
                    eval_results = self.evaluate(eval_texts)
                    print(f"Eval Loss: {eval_results['eval_loss']:.4f}, "
                          f"Perplexity: {eval_results['perplexity']:.2f}")

                # Save checkpoint
                if self.step % self.config.save_interval == 0:
                    checkpoint_path = os.path.join(save_dir, f"checkpoint_step_{self.step}.pkl")
                    self.save_model(checkpoint_path)

                # Stop if max steps reached
                if self.step >= self.config.max_steps:
                    break

            # Record epoch statistics
            avg_epoch_loss = epoch_loss / len(train_batches)
            self.training_history.append({
                "epoch": epoch,
                "step": self.step,
                "loss": avg_epoch_loss
            })

            print(f"Epoch {epoch} completed. Average loss: {avg_epoch_loss:.4f}")

            # Early stopping check
            if avg_epoch_loss < self.best_loss:
                self.best_loss = avg_epoch_loss
                best_model_path = os.path.join(save_dir, "best_model.pkl")
                self.save_model(best_model_path)

            if self.step >= self.config.max_steps:
                break

        print("Training completed!")

        # Final save
        final_model_path = os.path.join(save_dir, "final_model.pkl")
        self.save_model(final_model_path)
