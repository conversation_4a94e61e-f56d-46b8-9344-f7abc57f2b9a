# -*- coding: utf-8 -*-

"""Defines a simple toy task for testing the ASCE prototype."""

import random
import numpy as np

class SimpleContextualAssociationTask:
    """A simple task where input patterns map to different outputs based on context."""
    def __init__(self, num_input_neurons, num_output_neurons):
        """Initializes the task.

        Args:
            num_input_neurons: The number of input neurons the ASCE model expects.
            num_output_neurons: The number of output neurons the ASCE model has.
        """
        if num_input_neurons < 2 or num_output_neurons < 2:
            raise ValueError("Task requires at least 2 input and 2 output neurons.")

        self.num_input = num_input_neurons
        self.num_output = num_output_neurons
        self.current_context = 0

        # --- Define <PERSON>terns --- #
        # Represent patterns as vectors matching the number of input/output neurons
        # Example: Simple one-hot like patterns for 2 inputs/outputs
        self.input_patterns = {
            "A": np.array([1.0] + [0.0] * (num_input_neurons - 1)),
            "B": np.array([0.0, 1.0] + [0.0] * (num_input_neurons - 2))
        }
        self.output_patterns = {
            "X": np.array([1.0] + [0.0] * (num_output_neurons - 1)), # Target output neuron 0 active
            "Y": np.array([0.0, 1.0] + [0.0] * (num_output_neurons - 2)) # Target output neuron 1 active
        }

        # --- Define Contextual Rules --- #
        self.rules = {
            0: { # Context 0
                "A": "X", # Input A maps to Output X
                "B": "Y"  # Input B maps to Output Y
            },
            1: { # Context 1
                "A": "Y", # Input A maps to Output Y
                "B": "X"  # Input B maps to Output X
            }
        }
        self.pattern_keys = list(self.input_patterns.keys())

    def switch_context(self):
        """Switches the current context between 0 and 1."""
        self.current_context = 1 - self.current_context
        print(f"Task Context Switched to: {self.current_context}")
        return self.current_context

    def get_trial(self):
        """Gets a random input pattern and the current context."""
        pattern_key = random.choice(self.pattern_keys)
        input_vector = self.input_patterns[pattern_key]
        return pattern_key, input_vector, self.current_context

    def get_correct_output_key(self, input_key, context):
        """Gets the key (e.g., 'X' or 'Y') of the correct output for a given input and context."""
        return self.rules[context][input_key]

    def get_correct_output_vector(self, input_key, context):
        """Gets the target output vector for a given input and context."""
        output_key = self.get_correct_output_key(input_key, context)
        return self.output_patterns[output_key]

    def evaluate_response(self, input_key, context, network_output_vector):
        """Evaluates the network's output against the correct target.

        Args:
            input_key: The key of the input pattern presented (e.g., 'A').
            context: The context during the trial.
            network_output_vector: The output vector produced by the network
                                     (e.g., [1.0, 0.0] if first output neuron fired).

        Returns:
            A tuple: (is_correct, error_signal, target_vector)
            is_correct (bool): True if the output matches the target.
            error_signal (float): A measure of error (e.g., 0.0 for correct, 1.0 for incorrect).
            target_vector (np.array): The correct output vector.
        """
        target_vector = self.get_correct_output_vector(input_key, context)
        network_output_array = np.array(network_output_vector)

        # Simple evaluation: Check if the highest activation matches the target pattern's highest activation
        if np.argmax(network_output_array) == np.argmax(target_vector):
            is_correct = True
            error_signal = 0.0
        else:
            is_correct = False
            error_signal = 1.0 # Simple binary error
            # More nuanced error: distance between vectors, cross-entropy, etc.
            # error_signal = np.linalg.norm(target_vector - network_output_array)

        return is_correct, error_signal, target_vector

    def __repr__(self):
        return f"SimpleContextualAssociationTask(current_context={self.current_context})"

