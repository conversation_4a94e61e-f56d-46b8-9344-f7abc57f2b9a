# -*- coding: utf-8 -*-

"""Advanced ASCE Main - Complete Language Model Training and Inference System."""

import os
import sys
import argparse
import json
import time
from typing import List, Dict, Any

# Add training directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'training'))

from training.llm_trainer import ASCELanguageModel, TrainingConfig

def load_sample_texts() -> List[str]:
    """Load sample texts for training and evaluation."""
    sample_texts = [
        "The quick brown fox jumps over the lazy dog. This is a simple sentence for testing.",
        "Artificial intelligence is transforming the world in unprecedented ways.",
        "The ASCE model combines neuroscience principles with advanced AI architectures.",
        "Language models learn to predict the next word in a sequence of text.",
        "Deep learning has revolutionized natural language processing tasks.",
        "Neural networks can learn complex patterns from large datasets.",
        "The human brain processes information in a highly efficient manner.",
        "Sparse networks reduce computational requirements while maintaining performance.",
        "Event-driven plasticity enables rapid learning and adaptation.",
        "Context-dependent processing allows for flexible behavior in dynamic environments.",
        "Curiosity-driven learning promotes autonomous exploration and knowledge acquisition.",
        "Abstract memory systems store compressed representations of experiences.",
        "Modulatory systems control attention, learning, and behavioral states.",
        "Homeostatic mechanisms maintain network stability and optimal performance.",
        "Predictive coding minimizes surprise and enhances learning efficiency.",
        "The integration of multiple learning mechanisms creates robust AI systems.",
        "Biological inspiration guides the development of more efficient algorithms.",
        "Sparse representations enable energy-efficient computation in neural networks.",
        "Adaptive forgetting prevents catastrophic interference in continual learning.",
        "Multi-scale processing captures both local details and global patterns.",
        "Attention mechanisms focus computational resources on relevant information.",
        "Working memory maintains temporary information for complex reasoning tasks.",
        "Emotional modulation influences learning and decision-making processes.",
        "Hierarchical organization enables abstraction and generalization capabilities.",
        "Dynamic connectivity allows networks to adapt their structure over time.",
        "Neuromodulation provides global control signals for learning and behavior.",
        "Contextual adaptation enables flexible responses to changing environments.",
        "Intrinsic motivation drives exploration and skill acquisition.",
        "Meta-learning allows systems to learn how to learn more effectively.",
        "Transfer learning leverages knowledge from one domain to improve performance in another.",
        "Few-shot learning enables rapid adaptation to new tasks with minimal examples.",
        "Continual learning maintains performance on old tasks while learning new ones.",
        "Multimodal learning integrates information from different sensory modalities.",
        "Reinforcement learning optimizes behavior through trial and error.",
        "Unsupervised learning discovers hidden patterns in unlabeled data.",
        "Self-supervised learning creates learning signals from the data itself.",
        "Representation learning discovers useful features for downstream tasks.",
        "Generative models learn to create new data similar to training examples.",
        "Discriminative models learn to classify or predict target variables.",
        "Ensemble methods combine multiple models to improve overall performance.",
        "Regularization techniques prevent overfitting and improve generalization.",
        "Optimization algorithms find the best parameters for neural networks.",
        "Gradient descent iteratively improves model parameters using error signals.",
        "Backpropagation efficiently computes gradients for neural network training.",
        "Activation functions introduce non-linearity into neural computations.",
        "Loss functions measure the difference between predictions and targets.",
        "Evaluation metrics assess model performance on specific tasks.",
        "Cross-validation provides robust estimates of model generalization.",
        "Hyperparameter tuning optimizes model architecture and training settings.",
        "Data augmentation increases training data diversity and model robustness."
    ]
    
    return sample_texts

def create_training_config() -> TrainingConfig:
    """Create training configuration for ASCE language model."""
    return TrainingConfig(
        vocab_size=5000,  # Smaller vocab for demo
        sequence_length=128,  # Shorter sequences for efficiency
        embedding_dim=256,  # Smaller embeddings
        num_layers=6,  # Fewer layers for demo
        attention_heads=8,
        batch_size=8,  # Smaller batch size
        learning_rate=0.001,
        warmup_steps=100,
        max_steps=5000,  # Fewer steps for demo
        save_interval=500,
        eval_interval=250
    )

def train_asce_model(texts: List[str], config: TrainingConfig, save_dir: str = "asce_checkpoints"):
    """Train the ASCE language model."""
    print("=" * 60)
    print("ASCE LANGUAGE MODEL TRAINING")
    print("=" * 60)
    
    # Create model
    model = ASCELanguageModel(config)
    
    # Split data for training and evaluation
    split_idx = int(len(texts) * 0.8)
    train_texts = texts[:split_idx]
    eval_texts = texts[split_idx:]
    
    print(f"Training texts: {len(train_texts)}")
    print(f"Evaluation texts: {len(eval_texts)}")
    
    # Start training
    start_time = time.time()
    model.train(train_texts, eval_texts, save_dir)
    end_time = time.time()
    
    print(f"\nTraining completed in {end_time - start_time:.2f} seconds")
    
    return model

def test_text_generation(model: ASCELanguageModel):
    """Test text generation capabilities."""
    print("\n" + "=" * 60)
    print("TEXT GENERATION TESTING")
    print("=" * 60)
    
    test_prompts = [
        "The quick brown",
        "Artificial intelligence",
        "Neural networks",
        "The ASCE model",
        "Language models"
    ]
    
    for prompt in test_prompts:
        print(f"\nPrompt: '{prompt}'")
        print("-" * 40)
        
        # Generate with different temperatures
        for temp in [0.5, 1.0, 1.5]:
            generated = model.generate_text(prompt, max_length=20, temperature=temp)
            print(f"Temperature {temp}: {generated}")

def demonstrate_asce_capabilities(model: ASCELanguageModel):
    """Demonstrate advanced ASCE capabilities."""
    print("\n" + "=" * 60)
    print("ASCE ADVANCED CAPABILITIES DEMONSTRATION")
    print("=" * 60)
    
    # Show modulatory system state
    print("\n1. Modulatory System State:")
    ms_stats = model.ms.get_system_statistics()
    print(f"   Curiosity Level: {ms_stats['motivation']['intrinsic_motivation']:.3f}")
    print(f"   Attention Mode: {ms_stats['attention']['mode']}")
    print(f"   Emotional Valence: {ms_stats['emotion']['valence']:.3f}")
    print(f"   Learning Rate: {model.ms.get_learning_modulation():.6f}")
    
    # Show memory system statistics
    print("\n2. Abstract Memory System:")
    ams_stats = model.ams.get_memory_statistics()
    print(f"   Total Memory Traces: {ams_stats['total_traces']}")
    print(f"   Memory Usage: {ams_stats['memory_usage']:.2%}")
    print(f"   Average Strength: {ams_stats['average_strength']:.3f}")
    
    # Show CPU layer statistics
    print("\n3. CPU Layer Statistics:")
    for i, cpu in enumerate(model.cpu_layers):
        cpu_stats = cpu.get_cpu_statistics()
        print(f"   Layer {i}: {cpu_stats['network_stats']['total_neurons']} neurons, "
              f"Activity: {cpu_stats['network_stats']['current_activity']:.3f}")
    
    # Show neuromodulator levels
    print("\n4. Neuromodulator Levels:")
    neuromod = model.ms.get_neuromodulator_levels()
    for nt, level in neuromod.items():
        print(f"   {nt.capitalize()}: {level:.3f}")

def interactive_mode(model: ASCELanguageModel):
    """Interactive text generation mode."""
    print("\n" + "=" * 60)
    print("INTERACTIVE MODE")
    print("=" * 60)
    print("Enter prompts for text generation (type 'quit' to exit)")
    
    while True:
        try:
            prompt = input("\nPrompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                break
            
            if not prompt:
                continue
            
            # Generate text
            print("Generating...")
            generated = model.generate_text(prompt, max_length=50, temperature=1.0)
            print(f"Generated: {generated}")
            
            # Show system state
            curiosity = model.ms.get_curiosity_signal()
            learning_rate = model.ms.get_learning_modulation()
            print(f"System State - Curiosity: {curiosity:.3f}, LR: {learning_rate:.6f}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("Exiting interactive mode...")

def main():
    """Main function for ASCE language model demonstration."""
    parser = argparse.ArgumentParser(description="ASCE Language Model Training and Inference")
    parser.add_argument("--mode", choices=["train", "generate", "interactive", "demo"], 
                       default="demo", help="Operation mode")
    parser.add_argument("--model-path", type=str, help="Path to saved model")
    parser.add_argument("--save-dir", type=str, default="asce_checkpoints", 
                       help="Directory to save checkpoints")
    parser.add_argument("--prompt", type=str, help="Text prompt for generation")
    parser.add_argument("--max-length", type=int, default=50, help="Maximum generation length")
    parser.add_argument("--temperature", type=float, default=1.0, help="Generation temperature")
    
    args = parser.parse_args()
    
    print("ASCE - Adaptive Sparse Cognitive Engine")
    print("Advanced Language Model with Neuroscience-Inspired Architecture")
    print("=" * 80)
    
    # Load sample texts
    texts = load_sample_texts()
    config = create_training_config()
    
    if args.mode == "train":
        # Training mode
        model = train_asce_model(texts, config, args.save_dir)
        
        # Test generation after training
        test_text_generation(model)
        demonstrate_asce_capabilities(model)
        
    elif args.mode == "generate":
        # Generation mode
        if not args.model_path:
            print("Error: --model-path required for generation mode")
            return
        
        model = ASCELanguageModel(config)
        model.load_model(args.model_path)
        
        if args.prompt:
            generated = model.generate_text(args.prompt, args.max_length, args.temperature)
            print(f"Prompt: {args.prompt}")
            print(f"Generated: {generated}")
        else:
            test_text_generation(model)
    
    elif args.mode == "interactive":
        # Interactive mode
        if args.model_path:
            model = ASCELanguageModel(config)
            model.load_model(args.model_path)
        else:
            print("Training a quick model for interactive mode...")
            model = train_asce_model(texts[:20], config, args.save_dir)  # Quick training
        
        interactive_mode(model)
    
    else:  # demo mode
        # Full demonstration
        print("Running full ASCE demonstration...")
        
        # Train model
        model = train_asce_model(texts, config, args.save_dir)
        
        # Test capabilities
        test_text_generation(model)
        demonstrate_asce_capabilities(model)
        
        # Interactive session
        print("\nStarting interactive session...")
        interactive_mode(model)
    
    print("\nASCE demonstration completed!")

if __name__ == "__main__":
    main()
