#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Simple test for ASCE language model generation."""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

def test_simple_generation():
    """Test simple text generation without full training."""
    print("=" * 60)
    print("ASCE SIMPLE GENERATION TEST")
    print("=" * 60)
    
    try:
        from training.llm_trainer import ASCELanguageModel, TrainingConfig
        
        # Create very small config for quick testing
        config = TrainingConfig(
            vocab_size=100,
            sequence_length=16,
            embedding_dim=32,
            num_layers=2,
            batch_size=1,
            max_steps=5
        )
        
        print("Creating ASCE Language Model...")
        model = ASCELanguageModel(config)
        
        # Simple vocabulary
        simple_texts = [
            "hello world",
            "artificial intelligence",
            "neural networks",
            "machine learning",
            "deep learning"
        ]
        
        print("Building vocabulary...")
        model.data_processor.build_vocabulary(simple_texts)
        print(f"Vocabulary size: {len(model.data_processor.vocab)}")
        
        # Test encoding/decoding
        test_text = "hello world"
        encoded = model.data_processor.encode_text(test_text, max_length=8)
        decoded = model.data_processor.decode_tokens(encoded)
        print(f"Original: '{test_text}'")
        print(f"Encoded: {encoded}")
        print(f"Decoded: '{decoded}'")
        
        # Test forward pass
        print("\nTesting forward pass...")
        input_batch = np.array([encoded[:config.sequence_length]])
        logits = model.forward(input_batch, training=False)
        print(f"Input shape: {input_batch.shape}")
        print(f"Output shape: {logits.shape}")
        
        # Test generation
        print("\nTesting text generation...")
        try:
            generated = model.generate_text("hello", max_length=5, temperature=1.0)
            print(f"Generated: '{generated}'")
        except Exception as e:
            print(f"Generation failed: {e}")
            # Try simpler generation
            print("Trying simpler generation...")
            
            # Manual generation
            prompt_tokens = model.data_processor.encode_text("hello", max_length=4)
            print(f"Prompt tokens: {prompt_tokens}")
            
            # Get next token prediction
            input_seq = np.array([prompt_tokens[:config.sequence_length]])
            logits = model.forward(input_seq, training=False)
            
            # Get last position logits
            last_logits = logits[0, -1, :]
            
            # Find most likely token
            next_token = np.argmax(last_logits)
            print(f"Next token ID: {next_token}")
            
            if next_token in model.data_processor.reverse_vocab:
                next_word = model.data_processor.reverse_vocab[next_token]
                print(f"Next word: '{next_word}'")
            
        # Test system statistics
        print("\nSystem Statistics:")
        print("=" * 40)
        
        # CPU statistics
        for i, cpu in enumerate(model.cpu_layers):
            stats = cpu.get_cpu_statistics()
            print(f"CPU Layer {i}:")
            print(f"  Neurons: {stats['network_stats']['total_neurons']}")
            print(f"  Connections: {stats['network_stats']['total_connections']}")
            print(f"  Activity: {stats['network_stats']['current_activity']:.3f}")
        
        # Memory statistics
        ams_stats = model.ams.get_memory_statistics()
        print(f"\nMemory System:")
        print(f"  Total traces: {ams_stats['total_traces']}")
        print(f"  Memory usage: {ams_stats['memory_usage']:.2%}")
        
        # Modulatory system
        ms_stats = model.ms.get_system_statistics()
        print(f"\nModulatory System:")
        print(f"  Curiosity: {ms_stats['motivation']['intrinsic_motivation']:.3f}")
        print(f"  Attention mode: {ms_stats['attention']['mode']}")
        print(f"  Learning rate: {model.ms.get_learning_modulation():.6f}")
        
        # Neuromodulators
        neuromod = model.ms.get_neuromodulator_levels()
        print(f"\nNeuromodulators:")
        for nt, level in neuromod.items():
            print(f"  {nt.capitalize()}: {level:.3f}")
        
        print("\n" + "=" * 60)
        print("SIMPLE TEST COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_component_interaction():
    """Test interaction between components."""
    print("\n" + "=" * 60)
    print("COMPONENT INTERACTION TEST")
    print("=" * 60)
    
    try:
        from components.cpu import AdvancedCPU
        from components.ams import AdvancedAMS, MemoryType
        from components.ms import AdvancedModulatorySystem
        
        # Create components
        cpu = AdvancedCPU(cpu_id=0, architecture_config={
            "input_size": 8,
            "output_size": 4,
            "hidden_layers": [8],
            "sparsity_level": 0.3
        })
        
        ams = AdvancedAMS(ams_id=0, representation_size=4)
        ms = AdvancedModulatorySystem(ms_id=0)
        
        print("Components created successfully!")
        
        # Simulation loop
        for step in range(10):
            print(f"\nStep {step + 1}:")
            
            # Generate random input
            input_data = np.random.rand(8)
            
            # Process through CPU
            cpu.apply_input(input_data)
            cpu_result = cpu.step(modulatory_signals=ms.get_neuromodulator_levels())
            
            # Get CPU output
            output = cpu.get_output(output_format="vector")
            print(f"  CPU output: {output[:4]}")
            
            # Store significant patterns in AMS
            if np.max(output) > 0.5:
                ams.store_pattern(output, MemoryType.WORKING, {"step": step})
                print(f"  Pattern stored in AMS")
            
            # Update modulatory system
            system_feedback = {
                "prediction_error": np.random.rand() * 0.5,
                "task_success": np.random.rand() > 0.5,
                "novelty_level": np.random.rand()
            }
            ms.update_state(system_feedback)
            
            print(f"  Curiosity: {ms.get_curiosity_signal():.3f}")
            print(f"  Learning rate: {ms.get_learning_modulation():.6f}")
        
        # Final statistics
        print(f"\nFinal Statistics:")
        print(f"  AMS traces: {len(ams.memory_traces)}")
        print(f"  CPU activity: {cpu.get_cpu_statistics()['network_stats']['current_activity']:.3f}")
        print(f"  MS curiosity: {ms.get_curiosity_signal():.3f}")
        
        print("\nComponent interaction test completed!")
        
    except Exception as e:
        print(f"Interaction test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_generation()
    test_component_interaction()
