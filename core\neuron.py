# -*- coding: utf-8 -*-

"""Defines the basic Neuron unit for the ASCE prototype."""

import random

class Neuron:
    """Represents a single processing unit in the sparse network."""
    def __init__(self, neuron_id, threshold=0.5, potential=0.0, refractory_period=0):
        """Initializes a Neuron.

        Args:
            neuron_id: A unique identifier for the neuron.
            threshold: The activation threshold.
            potential: The current membrane potential (or activation level).
            refractory_period: Ticks remaining until the neuron can fire again.
        """
        self.id = neuron_id
        self.threshold = threshold
        self.potential = potential
        self.refractory_period = refractory_period
        self.fired_this_tick = False # Flag to indicate firing in the current simulation step
        self.inputs_this_tick = 0.0 # Accumulator for inputs in the current tick

    def integrate_input(self, input_value):
        """Adds an input value to the neuron's potential for the current tick."""
        if self.refractory_period <= 0:
            self.inputs_this_tick += input_value

    def update_potential(self, decay_rate=0.1):
        """Updates the neuron's potential based on integrated inputs and decay."""
        if self.refractory_period > 0:
            self.refractory_period -= 1
            self.potential = 0.0 # Clamp potential during refractory period
            self.fired_this_tick = False
        else:
            # Apply decay
            self.potential *= (1.0 - decay_rate)
            # Add integrated inputs
            self.potential += self.inputs_this_tick
            # Reset input accumulator for the next tick
            self.inputs_this_tick = 0.0

            # Check for firing
            if self.potential >= self.threshold:
                self.fire()
            else:
                self.fired_this_tick = False

    def fire(self, refractory_set=5):
        """Marks the neuron as fired and resets potential/sets refractory period."""
        self.fired_this_tick = True
        self.potential = 0.0 # Reset potential after firing (can be customized)
        self.refractory_period = refractory_set
        # print(f"Neuron {self.id} fired!") # Optional: for debugging

    def __repr__(self):
        return f"Neuron(id={self.id}, potential={self.potential:.2f}, refractory={self.refractory_period}, fired={self.fired_this_tick})"

