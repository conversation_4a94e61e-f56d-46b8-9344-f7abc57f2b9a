# -*- coding: utf-8 -*-

"""Enhanced Neuron implementation for the ASCE final model with advanced features."""

import random
import numpy as np
from typing import Dict, List, Optional, Tuple
import math

class AdvancedNeuron:
    """
    Advanced neuron implementation with sophisticated dynamics for ASCE.

    Features:
    - Multiple neurotransmitter types
    - Adaptive thresholds
    - Spike timing dependent plasticity (STDP) traces
    - Homeostatic mechanisms
    - Dendritic computation
    """

    def __init__(self, neuron_id: int, neuron_type: str = "excitatory",
                 threshold: float = 0.5, potential: float = 0.0,
                 adaptation_rate: float = 0.01, homeostatic_target: float = 0.1):
        """
        Initialize an advanced neuron.

        Args:
            neuron_id: Unique identifier
            neuron_type: 'excitatory', 'inhibitory', or 'modulatory'
            threshold: Base activation threshold
            potential: Initial membrane potential
            adaptation_rate: Rate of threshold adaptation
            homeostatic_target: Target firing rate for homeostasis
        """
        self.id = neuron_id
        self.type = neuron_type
        self.base_threshold = threshold
        self.threshold = threshold
        self.potential = potential
        self.adaptation_rate = adaptation_rate
        self.homeostatic_target = homeostatic_target

        # Advanced dynamics
        self.refractory_period = 0
        self.max_refractory = 5
        self.fired_this_tick = False
        self.firing_history = []  # Recent firing times
        self.firing_rate = 0.0

        # Input integration
        self.inputs_this_tick = 0.0
        self.dendritic_inputs = {}  # Separate dendritic compartments
        self.leak_conductance = 0.1
        self.membrane_time_constant = 10.0

        # STDP traces for plasticity
        self.pre_trace = 0.0  # Pre-synaptic trace
        self.post_trace = 0.0  # Post-synaptic trace
        self.trace_decay = 0.95

        # Neuromodulation sensitivity
        self.dopamine_sensitivity = 1.0
        self.acetylcholine_sensitivity = 1.0
        self.norepinephrine_sensitivity = 1.0

        # Homeostatic variables
        self.activity_history = []
        self.intrinsic_excitability = 1.0

        # Computational state
        self.prediction_error = 0.0
        self.surprise = 0.0
        self.information_content = 0.0

    def integrate_input(self, input_value: float, source_type: str = "default",
                       neurotransmitter: str = "glutamate"):
        """
        Integrate input with advanced dendritic processing.

        Args:
            input_value: Input strength
            source_type: Type of input source
            neurotransmitter: Neurotransmitter type affecting integration
        """
        if self.refractory_period <= 0:
            # Apply neurotransmitter-specific modulation
            modulated_input = self._apply_neurotransmitter_modulation(input_value, neurotransmitter)

            # Dendritic integration
            if source_type not in self.dendritic_inputs:
                self.dendritic_inputs[source_type] = 0.0
            self.dendritic_inputs[source_type] += modulated_input

            # Sum all dendritic inputs with non-linear integration
            total_dendritic = self._compute_dendritic_integration()
            self.inputs_this_tick += total_dendritic

    def _apply_neurotransmitter_modulation(self, input_value: float, neurotransmitter: str) -> float:
        """Apply neurotransmitter-specific modulation to input."""
        modulation_factors = {
            "glutamate": 1.0,  # Excitatory
            "gaba": -0.8,      # Inhibitory
            "dopamine": self.dopamine_sensitivity,
            "acetylcholine": self.acetylcholine_sensitivity,
            "norepinephrine": self.norepinephrine_sensitivity
        }
        return input_value * modulation_factors.get(neurotransmitter, 1.0)

    def _compute_dendritic_integration(self) -> float:
        """Compute non-linear dendritic integration."""
        if not self.dendritic_inputs:
            return 0.0

        # Non-linear dendritic computation (simplified)
        total = sum(self.dendritic_inputs.values())
        # Apply saturation and threshold effects
        integrated = total / (1.0 + abs(total) * 0.1)  # Soft saturation

        # Reset dendritic inputs for next tick
        self.dendritic_inputs.clear()
        return integrated

    def update_potential(self, decay_rate: float = 0.1, dt: float = 1.0,
                        neuromodulation: Dict[str, float] = None):
        """
        Advanced potential update with sophisticated dynamics.

        Args:
            decay_rate: Membrane leak rate
            dt: Time step
            neuromodulation: Dictionary of neuromodulator concentrations
        """
        if neuromodulation is None:
            neuromodulation = {}

        if self.refractory_period > 0:
            self.refractory_period -= 1
            self.potential = 0.0
            self.fired_this_tick = False
            self._update_traces(fired=False)
        else:
            # Membrane dynamics with time constant
            leak = self.potential * self.leak_conductance / self.membrane_time_constant
            self.potential -= leak * dt

            # Add integrated inputs
            self.potential += self.inputs_this_tick * self.intrinsic_excitability

            # Apply neuromodulation
            self._apply_neuromodulation(neuromodulation)

            # Adaptive threshold based on recent activity
            self._update_adaptive_threshold()

            # Check for firing
            if self.potential >= self.threshold:
                self.fire()
            else:
                self.fired_this_tick = False
                self._update_traces(fired=False)

            # Reset input accumulator
            self.inputs_this_tick = 0.0

    def _apply_neuromodulation(self, neuromodulation: Dict[str, float]):
        """Apply neuromodulatory effects to neuron dynamics."""
        # Dopamine affects reward prediction and plasticity
        if "dopamine" in neuromodulation:
            dopamine_level = neuromodulation["dopamine"]
            self.prediction_error = dopamine_level  # Simplified
            # Modulate threshold and excitability
            self.threshold *= (1.0 - dopamine_level * 0.1)

        # Acetylcholine affects attention and learning rate
        if "acetylcholine" in neuromodulation:
            ach_level = neuromodulation["acetylcholine"]
            self.intrinsic_excitability *= (1.0 + ach_level * 0.2)

        # Norepinephrine affects arousal and gain
        if "norepinephrine" in neuromodulation:
            ne_level = neuromodulation["norepinephrine"]
            self.potential *= (1.0 + ne_level * 0.15)

    def _update_adaptive_threshold(self):
        """Update threshold based on homeostatic mechanisms."""
        # Calculate recent firing rate
        current_time = len(self.activity_history)
        recent_activity = sum(1 for t in self.firing_history if current_time - t < 100)
        self.firing_rate = recent_activity / min(100, current_time + 1)

        # Homeostatic adjustment
        error = self.firing_rate - self.homeostatic_target
        threshold_adjustment = error * self.adaptation_rate
        self.threshold = max(0.1, self.threshold + threshold_adjustment)

    def fire(self):
        """Advanced firing with trace updates and history tracking."""
        self.fired_this_tick = True
        self.potential = 0.0
        self.refractory_period = self.max_refractory

        # Update firing history
        current_time = len(self.activity_history)
        self.firing_history.append(current_time)

        # Keep only recent history (last 1000 time steps)
        self.firing_history = [t for t in self.firing_history if current_time - t < 1000]

        # Update STDP traces
        self._update_traces(fired=True)

        # Calculate information content and surprise
        self._update_information_metrics()

    def _update_traces(self, fired: bool):
        """Update STDP traces for plasticity."""
        # Decay existing traces
        self.pre_trace *= self.trace_decay
        self.post_trace *= self.trace_decay

        if fired:
            # Boost post-synaptic trace on firing
            self.post_trace += 1.0

    def _update_information_metrics(self):
        """Update information-theoretic metrics."""
        # Simple surprise calculation based on firing probability
        expected_firing_prob = self.homeostatic_target
        if expected_firing_prob > 0:
            self.surprise = -math.log(expected_firing_prob)
            self.information_content = self.surprise

    def get_stdp_traces(self) -> Tuple[float, float]:
        """Get current STDP traces for plasticity calculations."""
        return self.pre_trace, self.post_trace

    def boost_pre_trace(self):
        """Boost pre-synaptic trace (called when providing input to other neurons)."""
        self.pre_trace += 1.0

    def get_state_dict(self) -> Dict:
        """Get complete neuron state for serialization."""
        return {
            'id': self.id,
            'type': self.type,
            'potential': self.potential,
            'threshold': self.threshold,
            'firing_rate': self.firing_rate,
            'intrinsic_excitability': self.intrinsic_excitability,
            'prediction_error': self.prediction_error,
            'surprise': self.surprise
        }

    def __repr__(self):
        return (f"AdvancedNeuron(id={self.id}, type={self.type}, "
                f"potential={self.potential:.3f}, threshold={self.threshold:.3f}, "
                f"fired={self.fired_this_tick}, rate={self.firing_rate:.3f})")


# Backward compatibility
class Neuron(AdvancedNeuron):
    """Backward compatible neuron class."""
    def __init__(self, neuron_id, threshold=0.5, potential=0.0, refractory_period=0):
        super().__init__(neuron_id, threshold=threshold, potential=potential)
        self.refractory_period = refractory_period

