# -*- coding: utf-8 -*-

"""Advanced Event-Driven Synaptic Plasticity (EDSP) implementation for ASCE final model."""

import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

class PlasticityType(Enum):
    """Types of plasticity mechanisms."""
    HEBBIAN = "hebbian"
    STDP = "stdp"
    HOMEOSTATIC = "homeostatic"
    METAPLASTIC = "metaplastic"
    CURIOSITY_DRIVEN = "curiosity_driven"
    PREDICTIVE = "predictive"

class LearningPhase(Enum):
    """Learning phases for different plasticity rules."""
    EXPLORATION = "exploration"
    EXPLOITATION = "exploitation"
    CONSOLIDATION = "consolidation"
    FORGETTING = "forgetting"

class AdvancedPlasticityEngine:
    """
    Advanced plasticity engine implementing multiple learning mechanisms.

    Features:
    - Multiple plasticity types (STDP, homeostatic, metaplastic)
    - Curiosity-driven learning
    - Predictive coding plasticity
    - Context-dependent learning rules
    - Active forgetting mechanisms
    """

    def __init__(self, learning_rate: float = 0.01, curiosity_factor: float = 1.0,
                 prediction_error_threshold: float = 0.5):
        """
        Initialize the plasticity engine.

        Args:
            learning_rate: Base learning rate
            curiosity_factor: Factor for curiosity-driven learning
            prediction_error_threshold: Threshold for prediction error-based learning
        """
        self.learning_rate = learning_rate
        self.curiosity_factor = curiosity_factor
        self.prediction_error_threshold = prediction_error_threshold

        # Learning phase management
        self.current_phase = LearningPhase.EXPLORATION
        self.phase_duration = 0
        self.phase_switch_threshold = 1000

        # Global learning statistics
        self.total_weight_changes = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.novelty_encounters = 0

        # Neuromodulation state
        self.dopamine_level = 0.5
        self.acetylcholine_level = 0.5
        self.norepinephrine_level = 0.5

        # Curiosity and exploration
        self.exploration_bonus = 1.0
        self.uncertainty_map = {}
        self.surprise_threshold = 1.0

        # Forgetting mechanisms
        self.forgetting_rate = 0.001
        self.forgetting_threshold = 0.1
        self.active_forgetting_enabled = True

    def apply_advanced_edsp(self, network, modulatory_signals: Dict[str, float] = None,
                           context_info: Dict[str, Any] = None) -> Dict[str, float]:
        """
        Apply advanced event-driven synaptic plasticity.

        Args:
            network: The neural network instance
            modulatory_signals: Neuromodulator concentrations
            context_info: Contextual information for learning

        Returns:
            Dictionary of learning statistics
        """
        if modulatory_signals is None:
            modulatory_signals = {}
        if context_info is None:
            context_info = {}

        # Update neuromodulation state
        self._update_neuromodulation(modulatory_signals)

        # Get fired neurons
        fired_neurons = {nid for nid, n in network.neurons.items() if n.fired_this_tick}

        if not fired_neurons:
            return {"weight_changes": 0, "plasticity_events": 0}

        # Apply different plasticity mechanisms
        stats = {"weight_changes": 0, "plasticity_events": 0, "novel_patterns": 0}

        # 1. STDP-based plasticity
        stdp_stats = self._apply_stdp_plasticity(network, fired_neurons, modulatory_signals)
        stats.update(stdp_stats)

        # 2. Curiosity-driven plasticity
        curiosity_stats = self._apply_curiosity_driven_plasticity(network, fired_neurons, context_info)
        stats["novel_patterns"] += curiosity_stats.get("novel_patterns", 0)

        # 3. Predictive coding plasticity
        prediction_stats = self._apply_predictive_plasticity(network, fired_neurons, context_info)
        stats["prediction_errors"] = prediction_stats.get("prediction_errors", 0)

        # 4. Homeostatic plasticity
        homeostatic_stats = self._apply_homeostatic_plasticity(network)
        stats["homeostatic_adjustments"] = homeostatic_stats.get("adjustments", 0)

        # 5. Active forgetting
        if self.active_forgetting_enabled:
            forgetting_stats = self._apply_active_forgetting(network, context_info)
            stats["forgotten_connections"] = forgetting_stats.get("pruned", 0)

        # Update learning phase
        self._update_learning_phase(stats)

        return stats

    def _update_neuromodulation(self, modulatory_signals: Dict[str, float]):
        """Update neuromodulator levels based on signals."""
        # Dopamine (reward prediction error)
        if "reward_prediction_error" in modulatory_signals:
            rpe = modulatory_signals["reward_prediction_error"]
            self.dopamine_level = max(0.0, min(1.0, 0.5 + rpe))

        # Acetylcholine (attention and uncertainty)
        if "uncertainty" in modulatory_signals:
            uncertainty = modulatory_signals["uncertainty"]
            self.acetylcholine_level = max(0.0, min(1.0, uncertainty))

        # Norepinephrine (arousal and stress)
        if "arousal" in modulatory_signals:
            arousal = modulatory_signals["arousal"]
            self.norepinephrine_level = max(0.0, min(1.0, arousal))

    def _apply_stdp_plasticity(self, network, fired_neurons: set,
                              modulatory_signals: Dict[str, float]) -> Dict[str, float]:
        """Apply Spike-Timing Dependent Plasticity."""
        weight_changes = 0
        plasticity_events = 0

        # Create neuromodulation dict
        neuromod = {
            "dopamine": self.dopamine_level,
            "acetylcholine": self.acetylcholine_level,
            "norepinephrine": self.norepinephrine_level
        }

        # Apply STDP to connections involving fired neurons
        for post_neuron_id in fired_neurons:
            post_neuron = network.get_neuron(post_neuron_id)
            if not post_neuron:
                continue

            # Get post-synaptic traces
            _, post_trace = post_neuron.get_stdp_traces()

            # Check incoming connections
            for pre_neuron_id, connection in network.incoming_connections.get(post_neuron_id, {}).items():
                pre_neuron = network.get_neuron(pre_neuron_id)
                if not pre_neuron:
                    continue

                # Get pre-synaptic traces
                pre_trace, _ = pre_neuron.get_stdp_traces()

                # Apply STDP if connection supports it
                if hasattr(connection, 'apply_stdp'):
                    delta_w = connection.apply_stdp(pre_trace, post_trace, neuromod)
                    weight_changes += abs(delta_w)
                    plasticity_events += 1

                # Boost pre-synaptic trace for future plasticity
                if pre_neuron_id in fired_neurons:
                    pre_neuron.boost_pre_trace()

        return {"weight_changes": weight_changes, "plasticity_events": plasticity_events}

    def _apply_curiosity_driven_plasticity(self, network, fired_neurons: set,
                                          context_info: Dict[str, Any]) -> Dict[str, float]:
        """Apply curiosity-driven learning mechanisms."""
        novel_patterns = 0

        # Calculate pattern novelty
        current_pattern = self._extract_activity_pattern(network, fired_neurons)
        novelty_score = self._calculate_novelty(current_pattern)

        if novelty_score > self.surprise_threshold:
            novel_patterns += 1
            self.novelty_encounters += 1

            # Boost learning for novel patterns
            curiosity_boost = self.curiosity_factor * novelty_score

            # Apply enhanced plasticity to active connections
            for post_neuron_id in fired_neurons:
                for pre_neuron_id, connection in network.incoming_connections.get(post_neuron_id, {}).items():
                    if pre_neuron_id in fired_neurons:
                        # Enhanced learning for novel co-activations
                        if hasattr(connection, 'update_weight'):
                            delta_w = self.learning_rate * curiosity_boost * 0.1
                            connection.update_weight(delta_w)

        # Update uncertainty map
        self._update_uncertainty_map(current_pattern, novelty_score)

        return {"novel_patterns": novel_patterns}

    def _apply_predictive_plasticity(self, network, fired_neurons: set,
                                   context_info: Dict[str, Any]) -> Dict[str, float]:
        """Apply predictive coding based plasticity."""
        prediction_errors = 0

        # Get prediction from context
        predicted_pattern = context_info.get("predicted_pattern", set())
        actual_pattern = fired_neurons

        # Calculate prediction error
        prediction_error = len(predicted_pattern.symmetric_difference(actual_pattern))
        prediction_error_normalized = prediction_error / max(len(predicted_pattern) + len(actual_pattern), 1)

        if prediction_error_normalized > self.prediction_error_threshold:
            prediction_errors += 1
            self.failed_predictions += 1

            # Apply error-driven plasticity
            error_signal = prediction_error_normalized

            # Strengthen connections that should have fired but didn't
            should_have_fired = predicted_pattern - actual_pattern
            for neuron_id in should_have_fired:
                for pre_id, connection in network.incoming_connections.get(neuron_id, {}).items():
                    if hasattr(connection, 'update_weight'):
                        delta_w = self.learning_rate * error_signal * 0.05
                        connection.update_weight(delta_w)

            # Weaken connections that fired but shouldn't have
            shouldnt_have_fired = actual_pattern - predicted_pattern
            for neuron_id in shouldnt_have_fired:
                for pre_id, connection in network.incoming_connections.get(neuron_id, {}).items():
                    if hasattr(connection, 'update_weight'):
                        delta_w = -self.learning_rate * error_signal * 0.03
                        connection.update_weight(delta_w)
        else:
            self.successful_predictions += 1

        return {"prediction_errors": prediction_errors}

    def _apply_homeostatic_plasticity(self, network) -> Dict[str, float]:
        """Apply homeostatic scaling to maintain network stability."""
        adjustments = 0

        # Calculate network-wide activity statistics
        total_activity = 0
        active_neurons = 0

        for neuron in network.neurons.values():
            if hasattr(neuron, 'firing_rate'):
                total_activity += neuron.firing_rate
                if neuron.firing_rate > 0:
                    active_neurons += 1

        if active_neurons > 0:
            avg_activity = total_activity / active_neurons
            target_activity = 0.1  # Target firing rate

            # Apply homeostatic scaling to connections
            for connections in network.outgoing_connections.values():
                for connection in connections.values():
                    if hasattr(connection, 'apply_homeostatic_scaling'):
                        connection.apply_homeostatic_scaling(target_activity, avg_activity)
                        adjustments += 1

        return {"adjustments": adjustments}

    def _apply_active_forgetting(self, network, context_info: Dict[str, Any]) -> Dict[str, float]:
        """Apply active forgetting mechanisms."""
        pruned_connections = 0

        # Get current context for relevance assessment
        current_context = context_info.get("context", "default")

        # Identify connections to prune
        connections_to_prune = []

        for pre_id, connections in network.outgoing_connections.items():
            for post_id, connection in connections.items():
                # Check if connection should be pruned
                if hasattr(connection, 'should_be_pruned') and connection.should_be_pruned():
                    connections_to_prune.append((pre_id, post_id))
                elif hasattr(connection, 'usage_trace'):
                    # Prune based on usage and relevance
                    if (connection.usage_trace < self.forgetting_threshold and
                        connection.weight < self.forgetting_threshold):
                        connections_to_prune.append((pre_id, post_id))

        # Prune identified connections
        for pre_id, post_id in connections_to_prune:
            if (pre_id in network.outgoing_connections and
                post_id in network.outgoing_connections[pre_id]):
                del network.outgoing_connections[pre_id][post_id]
                pruned_connections += 1

            if (post_id in network.incoming_connections and
                pre_id in network.incoming_connections[post_id]):
                del network.incoming_connections[post_id][pre_id]

        return {"pruned": pruned_connections}

    def _extract_activity_pattern(self, network, fired_neurons: set) -> str:
        """Extract a pattern representation from network activity."""
        # Simple pattern representation as sorted neuron IDs
        return ",".join(map(str, sorted(fired_neurons)))

    def _calculate_novelty(self, pattern: str) -> float:
        """Calculate novelty score for a given pattern."""
        if pattern not in self.uncertainty_map:
            return 1.0  # Completely novel

        # Calculate novelty based on frequency
        frequency = self.uncertainty_map[pattern]
        novelty = 1.0 / (1.0 + frequency)
        return novelty

    def _update_uncertainty_map(self, pattern: str, novelty_score: float):
        """Update the uncertainty map with new pattern observations."""
        if pattern not in self.uncertainty_map:
            self.uncertainty_map[pattern] = 0

        self.uncertainty_map[pattern] += 1

        # Decay old patterns to maintain relevance
        for p in list(self.uncertainty_map.keys()):
            self.uncertainty_map[p] *= 0.999
            if self.uncertainty_map[p] < 0.001:
                del self.uncertainty_map[p]

    def _update_learning_phase(self, stats: Dict[str, float]):
        """Update the current learning phase based on statistics."""
        self.phase_duration += 1

        # Phase switching logic
        if self.current_phase == LearningPhase.EXPLORATION:
            # Switch to exploitation if finding many novel patterns
            if stats.get("novel_patterns", 0) > 5:
                self.current_phase = LearningPhase.EXPLOITATION
                self.phase_duration = 0

        elif self.current_phase == LearningPhase.EXPLOITATION:
            # Switch to consolidation if learning is stable
            if (stats.get("prediction_errors", 0) < 2 and
                self.phase_duration > self.phase_switch_threshold):
                self.current_phase = LearningPhase.CONSOLIDATION
                self.phase_duration = 0

        elif self.current_phase == LearningPhase.CONSOLIDATION:
            # Switch back to exploration periodically
            if self.phase_duration > self.phase_switch_threshold * 2:
                self.current_phase = LearningPhase.EXPLORATION
                self.phase_duration = 0

    def get_learning_statistics(self) -> Dict[str, Any]:
        """Get comprehensive learning statistics."""
        total_predictions = self.successful_predictions + self.failed_predictions
        prediction_accuracy = (self.successful_predictions / max(total_predictions, 1))

        return {
            "learning_phase": self.current_phase.value,
            "total_weight_changes": self.total_weight_changes,
            "prediction_accuracy": prediction_accuracy,
            "novelty_encounters": self.novelty_encounters,
            "dopamine_level": self.dopamine_level,
            "acetylcholine_level": self.acetylcholine_level,
            "norepinephrine_level": self.norepinephrine_level,
            "uncertainty_patterns": len(self.uncertainty_map)
        }


# Legacy plasticity functions for backward compatibility
def apply_simple_hebbian_edsp(network, modulatory_signal=0.01):
    """Legacy Hebbian plasticity for backward compatibility."""
    engine = AdvancedPlasticityEngine(learning_rate=modulatory_signal)
    return engine.apply_advanced_edsp(network)

def apply_stdp_edsp(network, modulatory_signal=1.0):
    """STDP-based plasticity."""
    engine = AdvancedPlasticityEngine(learning_rate=modulatory_signal * 0.01)
    modulatory_signals = {"reward_prediction_error": modulatory_signal - 0.5}
    return engine.apply_advanced_edsp(network, modulatory_signals)

def apply_curiosity_driven_edsp(network, modulatory_signal=1.0, context_info=None):
    """Curiosity-driven plasticity."""
    engine = AdvancedPlasticityEngine(
        learning_rate=0.01,
        curiosity_factor=modulatory_signal
    )
    return engine.apply_advanced_edsp(network, context_info=context_info or {})

def apply_predictive_edsp(network, modulatory_signal=1.0, predicted_pattern=None):
    """Predictive coding plasticity."""
    engine = AdvancedPlasticityEngine(learning_rate=0.01)
    context_info = {"predicted_pattern": predicted_pattern or set()}
    modulatory_signals = {"uncertainty": modulatory_signal}
    return engine.apply_advanced_edsp(network, modulatory_signals, context_info)

# Enhanced plasticity rules dictionary
PLASTICITY_RULES = {
    "simple_hebbian": apply_simple_hebbian_edsp,
    "stdp": apply_stdp_edsp,
    "curiosity_driven": apply_curiosity_driven_edsp,
    "predictive": apply_predictive_edsp,
    "advanced": lambda network, **kwargs: AdvancedPlasticityEngine().apply_advanced_edsp(network, **kwargs)
}

