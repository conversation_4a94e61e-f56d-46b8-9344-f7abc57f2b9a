# -*- coding: utf-8 -*-

"""Implements Event-Driven Synaptic Plasticity (EDSP) rules for the ASCE prototype."""

# Note: This is a very simplified placeholder for EDSP.
# Real implementations might involve STDP, third-factor modulation, etc.

def apply_simple_hebbian_edsp(network, modulatory_signal=0.01):
    """Applies a simple Hebbian-like rule only to connections involved in recent firing.

    Increases weight between co-active pre- and post-synaptic neurons.
    This is event-driven because it only considers neurons that fired.

    Args:
        network: The SparseNetwork instance.
        modulatory_signal: A factor scaling the weight change (e.g., learning rate).
    """
    # Identify neurons that fired in the current tick
    fired_this_tick_ids = {nid for nid, n in network.neurons.items() if n.fired_this_tick}

    if not fired_this_tick_ids:
        return # No plasticity if nothing fired

    # Iterate through connections *ending* at neurons that just fired
    for post_neuron_id in fired_this_tick_ids:
        # Check incoming connections to this fired neuron
        for pre_neuron_id, connection in network.incoming_connections.get(post_neuron_id, {}).items():
            pre_neuron = network.get_neuron(pre_neuron_id)
            # Check if the pre-synaptic neuron also fired recently (e.g., in the same tick or previous)
            # For simplicity here, we check if it fired *this* tick as well (synchronous firing)
            # A more realistic rule might use a trace of recent pre-synaptic activity.
            if pre_neuron and pre_neuron_id in fired_this_tick_ids: # Check for co-activation
                # Apply potentiation (increase weight)
                delta_w = modulatory_signal * 1.0 # Simple positive change for co-activation
                connection.update_weight(delta_w)
                # print(f"EDSP: Potentiating {connection} by {delta_w:.4f}") # Optional debug

            # --- Placeholder for Depression --- #
            # A more complete rule would include depression, e.g., if pre fires but post doesn't,
            # or based on timing differences (like STDP).
            # Example: if pre_neuron.fired_this_tick and post_neuron_id not in fired_this_tick_ids:
            #     delta_w = -modulatory_signal * 0.5
            #     connection.update_weight(delta_w)

# Example of how to potentially integrate different rules later
# def apply_stdp_edsp(network, modulatory_signal=1.0):
#     # Implementation of STDP logic based on firing times
#     pass

# Dictionary to select plasticity rule if needed
PLASTICITY_RULES = {
    "simple_hebbian": apply_simple_hebbian_edsp,
    # "stdp": apply_stdp_edsp,
}

