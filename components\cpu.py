# -*- coding: utf-8 -*-

"""Implements the Cognitive Processing Unit (CPU) component for the ASCE prototype."""

from core.sparse_network import SparseNetwork
from core.plasticity import PLASTICITY_RULES

class CPU:
    """Represents a Cognitive Processing Unit, containing a sparse network."""
    def __init__(self, cpu_id, input_neuron_ids=None, output_neuron_ids=None, plasticity_rule="simple_hebbian"):
        """Initializes a CPU.

        Args:
            cpu_id: A unique identifier for this CPU.
            input_neuron_ids: A list or set of neuron IDs within this CPU designated as inputs.
            output_neuron_ids: A list or set of neuron IDs within this CPU designated as outputs.
            plasticity_rule: The key for the plasticity rule to use from PLASTICITY_RULES.
        """
        self.id = cpu_id
        self.network = SparseNetwork()
        self.input_neuron_ids = set(input_neuron_ids) if input_neuron_ids else set()
        self.output_neuron_ids = set(output_neuron_ids) if output_neuron_ids else set()

        if plasticity_rule not in PLASTICITY_RULES:
            raise ValueError(f"Unknown plasticity rule: {plasticity_rule}")
        self.plasticity_func = PLASTICITY_RULES[plasticity_rule]

    def add_neuron(self, is_input=False, is_output=False, **kwargs):
        """Adds a neuron to the CPU's internal network."""
        neuron_id = self.network.add_neuron(**kwargs)
        if is_input:
            self.input_neuron_ids.add(neuron_id)
        if is_output:
            self.output_neuron_ids.add(neuron_id)
        return neuron_id

    def add_connection(self, pre_neuron_id, post_neuron_id, **kwargs):
        """Adds a connection within the CPU's internal network."""
        return self.network.add_connection(pre_neuron_id, post_neuron_id, **kwargs)

    def apply_input(self, input_vector):
        """Applies an input vector to the designated input neurons.

        Args:
            input_vector: A dictionary {neuron_id: input_value} or a list/array
                          where the index corresponds to the sorted input neuron IDs.
        """
        if isinstance(input_vector, dict):
            for neuron_id, value in input_vector.items():
                if neuron_id in self.input_neuron_ids:
                    self.network.stimulate_neuron(neuron_id, value)
                else:
                    print(f"Warning: Neuron {neuron_id} is not designated as an input for CPU {self.id}.")
        else: # Assume list/array corresponds to sorted input IDs
            sorted_input_ids = sorted(list(self.input_neuron_ids))
            if len(input_vector) != len(sorted_input_ids):
                raise ValueError(f"Input vector length ({len(input_vector)}) does not match number of input neurons ({len(sorted_input_ids)}).")
            for i, value in enumerate(input_vector):
                self.network.stimulate_neuron(sorted_input_ids[i], value)

    def get_output(self):
        """Retrieves the current activation state of the designated output neurons."""
        output_state = {}
        for neuron_id in self.output_neuron_ids:
            neuron = self.network.get_neuron(neuron_id)
            if neuron:
                # Output could be firing state (True/False) or potential
                output_state[neuron_id] = neuron.fired_this_tick # Or neuron.potential
        return output_state

    def step(self, modulatory_signal=1.0):
        """Performs one simulation step for the CPU's internal network."""
        fired_ids = self.network.step(apply_plasticity_func=self.plasticity_func, modulatory_signal=modulatory_signal)
        # Potentially interact with AMS or MS here based on fired_ids or output_state
        return fired_ids

    def __repr__(self):
        return f"CPU(id={self.id}, network={self.network}, inputs={len(self.input_neuron_ids)}, outputs={len(self.output_neuron_ids)})"

