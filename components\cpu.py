# -*- coding: utf-8 -*-

"""Enhanced Cognitive Processing Unit (CPU) for the ASCE final model with advanced capabilities."""

import numpy as np
import random
from typing import Dict, List, Optional, Set, Any, Tuple
from collections import defaultdict, deque

from core.sparse_network import AdvancedSparseNetwork, SparseNetwork
from core.plasticity import Advanced<PERSON><PERSON>ityEng<PERSON>, PLASTICITY_RULES
from core.connection import SynapseType, NeurotransmitterType

class ProcessingMode:
    """Different processing modes for the CPU."""
    FEEDFORWARD = "feedforward"
    RECURRENT = "recurrent"
    PREDICTIVE = "predictive"
    ATTENTION = "attention"
    WORKING_MEMORY = "working_memory"

class AdvancedCPU:
    """
    Advanced Cognitive Processing Unit with sophisticated neural processing capabilities.

    Features:
    - Multi-layer hierarchical processing
    - Attention mechanisms
    - Working memory
    - Predictive coding
    - Context-dependent processing
    - Dynamic routing
    - Sparse representation learning
    """

    def __init__(self, cpu_id: int, architecture_config: Dict[str, Any] = None,
                 plasticity_rule: str = "advanced"):
        """
        Initialize an advanced CPU.

        Args:
            cpu_id: Unique identifier for this CPU
            architecture_config: Configuration for network architecture
            plasticity_rule: Plasticity mechanism to use
        """
        self.id = cpu_id
        self.network = AdvancedSparseNetwork(
            enable_structural_plasticity=True,
            enable_homeostasis=True
        )

        # Architecture configuration
        self.config = architecture_config or self._get_default_config()

        # Neuron organization
        self.input_neuron_ids = set()
        self.output_neuron_ids = set()
        self.hidden_layers = {}  # {layer_name: set of neuron_ids}
        self.working_memory_ids = set()
        self.attention_ids = set()

        # Processing state
        self.current_mode = ProcessingMode.FEEDFORWARD
        self.attention_weights = {}
        self.working_memory_state = {}
        self.prediction_buffer = deque(maxlen=10)

        # Plasticity and learning
        if plasticity_rule in PLASTICITY_RULES:
            self.plasticity_func = PLASTICITY_RULES[plasticity_rule]
        else:
            self.plasticity_engine = AdvancedPlasticityEngine()
            self.plasticity_func = self.plasticity_engine.apply_advanced_edsp

        # Performance metrics
        self.processing_history = deque(maxlen=1000)
        self.prediction_accuracy = 0.0
        self.attention_efficiency = 0.0

        # Context and state
        self.context_vector = np.zeros(self.config.get("context_size", 64))
        self.internal_state = {}

        # Build initial architecture
        self._build_architecture()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default architecture configuration."""
        return {
            "input_size": 128,
            "output_size": 64,
            "hidden_layers": [256, 128, 64],
            "working_memory_size": 32,
            "attention_size": 16,
            "context_size": 64,
            "sparsity_level": 0.1,
            "recurrent_connections": True,
            "predictive_layers": True,
            "attention_mechanism": True
        }

    def _build_architecture(self):
        """Build the CPU's neural architecture."""
        # Create input layer
        input_layer_ids = []
        for i in range(self.config["input_size"]):
            neuron_id = self.network.add_neuron(
                neuron_type="excitatory",
                layer_id="input",
                threshold=0.6
            )
            input_layer_ids.append(neuron_id)
            self.input_neuron_ids.add(neuron_id)

        # Create hidden layers
        prev_layer_ids = input_layer_ids
        for layer_idx, layer_size in enumerate(self.config["hidden_layers"]):
            layer_name = f"hidden_{layer_idx}"
            layer_ids = []

            for i in range(layer_size):
                neuron_id = self.network.add_neuron(
                    neuron_type="excitatory",
                    layer_id=layer_name,
                    threshold=0.5
                )
                layer_ids.append(neuron_id)

            self.hidden_layers[layer_name] = set(layer_ids)

            # Connect to previous layer
            self._create_layer_connections(prev_layer_ids, layer_ids,
                                         self.config["sparsity_level"])

            prev_layer_ids = layer_ids

        # Create output layer
        output_layer_ids = []
        for i in range(self.config["output_size"]):
            neuron_id = self.network.add_neuron(
                neuron_type="excitatory",
                layer_id="output",
                threshold=0.5
            )
            output_layer_ids.append(neuron_id)
            self.output_neuron_ids.add(neuron_id)

        # Connect last hidden layer to output
        if prev_layer_ids:
            self._create_layer_connections(prev_layer_ids, output_layer_ids,
                                         self.config["sparsity_level"])

        # Add working memory neurons
        if self.config.get("working_memory_size", 0) > 0:
            self._create_working_memory()

        # Add attention mechanism
        if self.config.get("attention_mechanism", False):
            self._create_attention_mechanism()

        # Add recurrent connections
        if self.config.get("recurrent_connections", False):
            self._add_recurrent_connections()

        # Add inhibitory neurons
        self._add_inhibitory_neurons()

    def _create_layer_connections(self, source_ids: List[int], target_ids: List[int],
                                 connection_probability: float):
        """Create sparse connections between layers."""
        for pre_id in source_ids:
            for post_id in target_ids:
                if random.random() < connection_probability:
                    weight = random.uniform(0.2, 0.8)
                    self.network.add_connection(
                        pre_neuron_id=pre_id,
                        post_neuron_id=post_id,
                        weight=weight,
                        synapse_type=SynapseType.EXCITATORY
                    )

    def _create_working_memory(self):
        """Create working memory neurons with recurrent connections."""
        wm_size = self.config["working_memory_size"]
        wm_ids = []

        for i in range(wm_size):
            neuron_id = self.network.add_neuron(
                neuron_type="excitatory",
                layer_id="working_memory",
                threshold=0.4,
                membrane_time_constant=20.0  # Longer time constant for persistence
            )
            wm_ids.append(neuron_id)
            self.working_memory_ids.add(neuron_id)

        # Create recurrent connections within working memory
        for i, pre_id in enumerate(wm_ids):
            for j, post_id in enumerate(wm_ids):
                if i != j and random.random() < 0.3:
                    weight = random.uniform(0.1, 0.4)
                    self.network.add_connection(pre_id, post_id, weight=weight)

        # Connect working memory to hidden layers
        for layer_ids in self.hidden_layers.values():
            for wm_id in wm_ids:
                for hidden_id in random.sample(list(layer_ids),
                                             min(10, len(layer_ids))):
                    # Bidirectional connections
                    self.network.add_connection(wm_id, hidden_id, weight=0.2)
                    self.network.add_connection(hidden_id, wm_id, weight=0.2)

    def _create_attention_mechanism(self):
        """Create attention neurons for selective processing."""
        att_size = self.config["attention_size"]
        att_ids = []

        for i in range(att_size):
            neuron_id = self.network.add_neuron(
                neuron_type="modulatory",
                layer_id="attention",
                threshold=0.3
            )
            att_ids.append(neuron_id)
            self.attention_ids.add(neuron_id)

        # Connect attention neurons to all layers for modulation
        all_layer_ids = (list(self.input_neuron_ids) +
                        [nid for layer in self.hidden_layers.values() for nid in layer] +
                        list(self.output_neuron_ids))

        for att_id in att_ids:
            for target_id in random.sample(all_layer_ids,
                                         min(50, len(all_layer_ids))):
                self.network.add_connection(
                    att_id, target_id,
                    weight=0.1,
                    synapse_type=SynapseType.MODULATORY,
                    neurotransmitter=NeurotransmitterType.ACETYLCHOLINE
                )

    def _add_recurrent_connections(self):
        """Add recurrent connections within and between layers."""
        for layer_name, layer_ids in self.hidden_layers.items():
            layer_list = list(layer_ids)

            # Intra-layer recurrent connections
            for i, pre_id in enumerate(layer_list):
                for j, post_id in enumerate(layer_list):
                    if i != j and random.random() < 0.05:
                        weight = random.uniform(0.1, 0.3)
                        self.network.add_connection(pre_id, post_id, weight=weight)

            # Inter-layer feedback connections
            for other_layer_name, other_layer_ids in self.hidden_layers.items():
                if layer_name != other_layer_name and random.random() < 0.02:
                    pre_id = random.choice(layer_list)
                    post_id = random.choice(list(other_layer_ids))
                    weight = random.uniform(0.05, 0.2)
                    self.network.add_connection(pre_id, post_id, weight=weight)

    def _add_inhibitory_neurons(self):
        """Add inhibitory neurons for network regulation."""
        total_excitatory = len(self.network.neurons)
        num_inhibitory = max(1, total_excitatory // 10)  # 10% inhibitory

        for i in range(num_inhibitory):
            neuron_id = self.network.add_neuron(
                neuron_type="inhibitory",
                layer_id="inhibitory",
                threshold=0.3
            )

            # Connect to random excitatory neurons
            excitatory_ids = [nid for nid, n in self.network.neurons.items()
                            if n.type == "excitatory"]

            targets = random.sample(excitatory_ids,
                                  min(20, len(excitatory_ids)))

            for target_id in targets:
                weight = random.uniform(0.3, 0.7)
                self.network.add_connection(
                    neuron_id, target_id,
                    weight=weight,
                    synapse_type=SynapseType.INHIBITORY,
                    neurotransmitter=NeurotransmitterType.GABA
                )

    def add_neuron(self, is_input: bool = False, is_output: bool = False,
                   layer_id: str = None, **kwargs) -> int:
        """Add a neuron to the CPU's network."""
        neuron_id = self.network.add_neuron(layer_id=layer_id, **kwargs)

        if is_input:
            self.input_neuron_ids.add(neuron_id)
        if is_output:
            self.output_neuron_ids.add(neuron_id)

        return neuron_id

    def add_connection(self, pre_neuron_id: int, post_neuron_id: int, **kwargs):
        """Add a connection within the CPU's network."""
        return self.network.add_connection(pre_neuron_id, post_neuron_id, **kwargs)

    def apply_input(self, input_data: Any, input_type: str = "vector"):
        """
        Apply input to the CPU with multiple input formats.

        Args:
            input_data: Input data (vector, dictionary, or sequence)
            input_type: Type of input ("vector", "dict", "sequence", "text")
        """
        if input_type == "vector":
            self._apply_vector_input(input_data)
        elif input_type == "dict":
            self._apply_dict_input(input_data)
        elif input_type == "sequence":
            self._apply_sequence_input(input_data)
        elif input_type == "text":
            self._apply_text_input(input_data)
        else:
            raise ValueError(f"Unknown input type: {input_type}")

    def _apply_vector_input(self, input_vector):
        """Apply vector input to input neurons."""
        if isinstance(input_vector, (list, tuple)):
            input_vector = np.array(input_vector)

        input_ids = sorted(list(self.input_neuron_ids))

        if len(input_vector) != len(input_ids):
            # Resize or pad input vector
            if len(input_vector) > len(input_ids):
                input_vector = input_vector[:len(input_ids)]
            else:
                padded = np.zeros(len(input_ids))
                padded[:len(input_vector)] = input_vector
                input_vector = padded

        for i, neuron_id in enumerate(input_ids):
            if i < len(input_vector):
                self.network.stimulate_neuron(neuron_id, float(input_vector[i]))

    def _apply_dict_input(self, input_dict: Dict[int, float]):
        """Apply dictionary input to specific neurons."""
        for neuron_id, value in input_dict.items():
            if neuron_id in self.input_neuron_ids:
                self.network.stimulate_neuron(neuron_id, value)

    def _apply_sequence_input(self, sequence: List[Any]):
        """Apply sequential input with temporal dynamics."""
        # Store sequence in working memory
        for i, item in enumerate(sequence[-10:]):  # Last 10 items
            if isinstance(item, (int, float)):
                # Apply to working memory neurons
                wm_ids = list(self.working_memory_ids)
                if wm_ids and i < len(wm_ids):
                    self.network.stimulate_neuron(wm_ids[i], float(item))

    def _apply_text_input(self, text: str):
        """Apply text input using simple encoding."""
        # Simple character-based encoding
        char_values = [ord(c) / 255.0 for c in text[:len(self.input_neuron_ids)]]
        self._apply_vector_input(char_values)

    def step(self, modulatory_signals: Dict[str, float] = None,
             context_info: Dict[str, Any] = None,
             processing_mode: str = None) -> Dict[str, Any]:
        """
        Perform one processing step with advanced dynamics.

        Args:
            modulatory_signals: Neuromodulator concentrations
            context_info: Contextual information
            processing_mode: Processing mode to use

        Returns:
            Dictionary with step results and statistics
        """
        if modulatory_signals is None:
            modulatory_signals = {}
        if context_info is None:
            context_info = {}

        # Set processing mode
        if processing_mode:
            self.current_mode = processing_mode

        # Update context
        self._update_context(context_info)

        # Apply attention if enabled
        if self.attention_ids:
            self._apply_attention_modulation(context_info)

        # Perform network step
        step_stats = self.network.step(
            apply_plasticity=True,
            modulatory_signals=modulatory_signals,
            context_info=context_info
        )

        # Update working memory
        if self.working_memory_ids:
            self._update_working_memory()

        # Generate predictions if in predictive mode
        if self.current_mode == ProcessingMode.PREDICTIVE:
            predictions = self._generate_predictions()
            step_stats["predictions"] = predictions

        # Update internal state
        self._update_internal_state(step_stats)

        # Record processing history
        self.processing_history.append({
            "time": self.network.current_time,
            "activity": step_stats.get("network_activity", 0),
            "mode": self.current_mode,
            "fired_neurons": len(step_stats.get("fired_neurons", set()))
        })

        return step_stats

    def _update_context(self, context_info: Dict[str, Any]):
        """Update internal context representation."""
        if "context_vector" in context_info:
            new_context = np.array(context_info["context_vector"])
            if len(new_context) == len(self.context_vector):
                # Blend with existing context
                self.context_vector = 0.7 * self.context_vector + 0.3 * new_context

        # Update internal state dictionary
        for key, value in context_info.items():
            if key != "context_vector":
                self.internal_state[key] = value

    def _apply_attention_modulation(self, context_info: Dict[str, Any]):
        """Apply attention-based modulation to network processing."""
        attention_target = context_info.get("attention_target", "input")
        attention_strength = context_info.get("attention_strength", 0.5)

        # Calculate attention weights
        if attention_target == "input":
            target_neurons = self.input_neuron_ids
        elif attention_target == "output":
            target_neurons = self.output_neuron_ids
        elif attention_target in self.hidden_layers:
            target_neurons = self.hidden_layers[attention_target]
        else:
            target_neurons = set()

        # Apply attention modulation
        for neuron_id in target_neurons:
            neuron = self.network.get_neuron(neuron_id)
            if neuron and hasattr(neuron, 'intrinsic_excitability'):
                neuron.intrinsic_excitability *= (1.0 + attention_strength)

    def _update_working_memory(self):
        """Update working memory state based on current activity."""
        wm_state = {}
        for neuron_id in self.working_memory_ids:
            neuron = self.network.get_neuron(neuron_id)
            if neuron:
                wm_state[neuron_id] = {
                    "potential": neuron.potential,
                    "fired": neuron.fired_this_tick,
                    "activity": getattr(neuron, 'firing_rate', 0.0)
                }

        self.working_memory_state = wm_state

    def _generate_predictions(self) -> Dict[str, Any]:
        """Generate predictions about future states."""
        predictions = {}

        # Simple prediction based on current activity patterns
        current_pattern = set()
        for neuron_id, neuron in self.network.neurons.items():
            if neuron.fired_this_tick:
                current_pattern.add(neuron_id)

        # Store current pattern for future comparison
        self.prediction_buffer.append(current_pattern)

        # Predict next pattern based on history
        if len(self.prediction_buffer) > 1:
            # Simple pattern-based prediction
            predicted_pattern = self._predict_next_pattern()
            predictions["next_pattern"] = predicted_pattern

            # Calculate prediction accuracy
            if len(self.prediction_buffer) > 2:
                actual = self.prediction_buffer[-1]
                predicted = self.prediction_buffer[-2]  # Previous prediction
                accuracy = len(actual.intersection(predicted)) / max(len(actual.union(predicted)), 1)
                self.prediction_accuracy = 0.9 * self.prediction_accuracy + 0.1 * accuracy
                predictions["accuracy"] = self.prediction_accuracy

        return predictions

    def _predict_next_pattern(self) -> set:
        """Predict the next firing pattern based on history."""
        if len(self.prediction_buffer) < 2:
            return set()

        # Simple prediction: neurons that fired in similar past contexts
        current = self.prediction_buffer[-1]
        predicted = set()

        # Look for patterns in history
        for i in range(len(self.prediction_buffer) - 2):
            past_pattern = self.prediction_buffer[i]
            next_pattern = self.prediction_buffer[i + 1]

            # If past pattern is similar to current, predict similar next
            similarity = len(current.intersection(past_pattern)) / max(len(current.union(past_pattern)), 1)
            if similarity > 0.5:
                predicted.update(next_pattern)

        return predicted

    def _update_internal_state(self, step_stats: Dict[str, Any]):
        """Update internal processing state."""
        self.internal_state.update({
            "last_activity": step_stats.get("network_activity", 0),
            "last_spikes": step_stats.get("total_spikes", 0),
            "energy_consumed": step_stats.get("energy_consumed", 0),
            "plasticity_events": step_stats.get("plasticity_events", 0)
        })

    def get_output(self, output_format: str = "firing") -> Any:
        """
        Get CPU output in various formats.

        Args:
            output_format: Format for output ("firing", "potential", "vector", "sparse")

        Returns:
            Output in requested format
        """
        if output_format == "firing":
            return self._get_firing_output()
        elif output_format == "potential":
            return self._get_potential_output()
        elif output_format == "vector":
            return self._get_vector_output()
        elif output_format == "sparse":
            return self._get_sparse_output()
        else:
            raise ValueError(f"Unknown output format: {output_format}")

    def _get_firing_output(self) -> Dict[int, bool]:
        """Get firing state of output neurons."""
        output_state = {}
        for neuron_id in self.output_neuron_ids:
            neuron = self.network.get_neuron(neuron_id)
            output_state[neuron_id] = neuron.fired_this_tick if neuron else False
        return output_state

    def _get_potential_output(self) -> Dict[int, float]:
        """Get membrane potential of output neurons."""
        output_state = {}
        for neuron_id in self.output_neuron_ids:
            neuron = self.network.get_neuron(neuron_id)
            output_state[neuron_id] = neuron.potential if neuron else 0.0
        return output_state

    def _get_vector_output(self) -> np.ndarray:
        """Get output as a vector."""
        output_ids = sorted(list(self.output_neuron_ids))
        output_vector = np.zeros(len(output_ids))

        for i, neuron_id in enumerate(output_ids):
            neuron = self.network.get_neuron(neuron_id)
            if neuron:
                output_vector[i] = 1.0 if neuron.fired_this_tick else 0.0

        return output_vector

    def _get_sparse_output(self) -> List[int]:
        """Get sparse representation of output (indices of firing neurons)."""
        firing_neurons = []
        for neuron_id in self.output_neuron_ids:
            neuron = self.network.get_neuron(neuron_id)
            if neuron and neuron.fired_this_tick:
                firing_neurons.append(neuron_id)
        return firing_neurons

    def get_cpu_statistics(self) -> Dict[str, Any]:
        """Get comprehensive CPU statistics."""
        network_stats = self.network.get_network_statistics()

        # Calculate layer-specific statistics
        layer_stats = {}
        for layer_name, layer_ids in self.hidden_layers.items():
            active_neurons = sum(1 for nid in layer_ids
                               if self.network.get_neuron(nid).fired_this_tick)
            layer_stats[layer_name] = {
                "size": len(layer_ids),
                "active": active_neurons,
                "activity_rate": active_neurons / len(layer_ids)
            }

        # Working memory statistics
        wm_stats = {}
        if self.working_memory_ids:
            wm_active = sum(1 for nid in self.working_memory_ids
                           if self.network.get_neuron(nid).fired_this_tick)
            wm_stats = {
                "size": len(self.working_memory_ids),
                "active": wm_active,
                "activity_rate": wm_active / len(self.working_memory_ids)
            }

        return {
            "cpu_id": self.id,
            "processing_mode": self.current_mode,
            "network_stats": network_stats,
            "layer_stats": layer_stats,
            "working_memory_stats": wm_stats,
            "prediction_accuracy": self.prediction_accuracy,
            "context_vector_norm": np.linalg.norm(self.context_vector),
            "total_processing_steps": len(self.processing_history)
        }

    def save_state(self) -> Dict[str, Any]:
        """Save complete CPU state."""
        return {
            "cpu_id": self.id,
            "config": self.config,
            "network_state": self.network.save_state(),
            "neuron_organization": {
                "input_ids": list(self.input_neuron_ids),
                "output_ids": list(self.output_neuron_ids),
                "hidden_layers": {k: list(v) for k, v in self.hidden_layers.items()},
                "working_memory_ids": list(self.working_memory_ids),
                "attention_ids": list(self.attention_ids)
            },
            "processing_state": {
                "current_mode": self.current_mode,
                "context_vector": self.context_vector.tolist(),
                "internal_state": self.internal_state,
                "prediction_accuracy": self.prediction_accuracy,
                "working_memory_state": self.working_memory_state
            }
        }

    def reset_state(self):
        """Reset CPU to initial state while preserving architecture."""
        self.network.reset_network_state()
        self.current_mode = ProcessingMode.FEEDFORWARD
        self.context_vector.fill(0.0)
        self.internal_state.clear()
        self.working_memory_state.clear()
        self.prediction_buffer.clear()
        self.processing_history.clear()
        self.prediction_accuracy = 0.0

    def __repr__(self):
        stats = self.get_cpu_statistics()
        return (f"AdvancedCPU(id={self.id}, "
                f"neurons={stats['network_stats']['total_neurons']}, "
                f"mode={self.current_mode}, "
                f"activity={stats['network_stats']['current_activity']:.3f})")


# Backward compatibility class
class CPU(AdvancedCPU):
    """Backward compatible CPU class."""

    def __init__(self, cpu_id, input_neuron_ids=None, output_neuron_ids=None,
                 plasticity_rule="simple_hebbian"):
        # Create simple configuration
        config = {
            "input_size": 10,
            "output_size": 5,
            "hidden_layers": [20],
            "working_memory_size": 0,
            "attention_size": 0,
            "sparsity_level": 0.3,
            "recurrent_connections": False,
            "predictive_layers": False,
            "attention_mechanism": False
        }

        super().__init__(cpu_id, config, plasticity_rule)

        # Override with provided neuron IDs if given
        if input_neuron_ids:
            self.input_neuron_ids = set(input_neuron_ids)
        if output_neuron_ids:
            self.output_neuron_ids = set(output_neuron_ids)

        # Use simple network for backward compatibility
        from core.sparse_network import SparseNetwork
        self.network = SparseNetwork()

        # Set up plasticity function
        if plasticity_rule not in PLASTICITY_RULES:
            raise ValueError(f"Unknown plasticity rule: {plasticity_rule}")
        self.plasticity_func = PLASTICITY_RULES[plasticity_rule]

    def add_neuron(self, is_input=False, is_output=False, **kwargs):
        """Add neuron with backward compatibility."""
        neuron_id = self.network.add_neuron(**kwargs)
        if is_input:
            self.input_neuron_ids.add(neuron_id)
        if is_output:
            self.output_neuron_ids.add(neuron_id)
        return neuron_id

    def add_connection(self, pre_neuron_id, post_neuron_id, **kwargs):
        """Add connection with backward compatibility."""
        return self.network.add_connection(pre_neuron_id, post_neuron_id, **kwargs)

    def apply_input(self, input_vector):
        """Apply input with backward compatibility."""
        if isinstance(input_vector, dict):
            for neuron_id, value in input_vector.items():
                if neuron_id in self.input_neuron_ids:
                    self.network.stimulate_neuron(neuron_id, value)
                else:
                    print(f"Warning: Neuron {neuron_id} is not designated as an input for CPU {self.id}.")
        else:
            sorted_input_ids = sorted(list(self.input_neuron_ids))
            if len(input_vector) != len(sorted_input_ids):
                raise ValueError(f"Input vector length ({len(input_vector)}) does not match number of input neurons ({len(sorted_input_ids)}).")
            for i, value in enumerate(input_vector):
                self.network.stimulate_neuron(sorted_input_ids[i], value)

    def get_output(self):
        """Get output with backward compatibility."""
        output_state = {}
        for neuron_id in self.output_neuron_ids:
            neuron = self.network.get_neuron(neuron_id)
            if neuron:
                output_state[neuron_id] = neuron.fired_this_tick
        return output_state

    def step(self, modulatory_signal=1.0):
        """Step with backward compatibility."""
        fired_ids = self.network.step(
            apply_plasticity_func=self.plasticity_func,
            modulatory_signal=modulatory_signal
        )
        return fired_ids

