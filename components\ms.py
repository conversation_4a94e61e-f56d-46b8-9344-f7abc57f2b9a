# -*- coding: utf-8 -*-

"""Implements a simplified Modulatory System (MS) for the ASCE prototype."""

import random

class ModulatorySystem:
    """Represents a basic Modulatory System.

    Generates global signals influencing learning, attention, and context.
    In this prototype, signals are simplified placeholders.
    """
    def __init__(self, ms_id):
        """Initializes the MS.

        Args:
            ms_id: A unique identifier for this MS.
        """
        self.id = ms_id
        self.current_context = 0 # Example: Simple integer representing context
        self.base_learning_rate = 0.01
        self.curiosity_level = 0.5 # Example: Normalized value (0-1)
        self.attention_focus = None # Example: Could point to a specific CPU or AMS trace ID

    def update_state(self, system_error=None, novelty_signal=None, external_context_cue=None):
        """Updates the internal state based on system feedback or external cues.

        Args:
            system_error: A measure of overall prediction error or task failure.
            novelty_signal: A measure of how novel the current input/situation is.
            external_context_cue: An explicit signal indicating a context change.
        """
        # --- Update Curiosity (Example Logic) ---
        if system_error is not None:
            # Higher error increases curiosity (up to a limit)
            self.curiosity_level = min(1.0, self.curiosity_level + system_error * 0.1)
        if novelty_signal is not None:
            # Novelty also increases curiosity
            self.curiosity_level = min(1.0, self.curiosity_level + novelty_signal * 0.2)
        # Curiosity decays over time if not stimulated
        self.curiosity_level *= 0.98

        # --- Update Context (Example Logic) ---
        if external_context_cue is not None:
            self.current_context = external_context_cue
        # Could also infer context based on persistent errors or specific input patterns

        # --- Update Attention (Placeholder) ---
        # Logic to determine where attention should be focused based on error, novelty, goals...
        self.attention_focus = None # Resetting for now

    def get_learning_modulation(self):
        """Calculates the current learning rate modulation signal.

        Example: Higher curiosity leads to a higher learning rate.
        """
        # Scale base rate by curiosity level (e.g., 0.5 to 1.5 times base rate)
        modulation_factor = 0.5 + self.curiosity_level
        return self.base_learning_rate * modulation_factor

    def get_context_signal(self):
        """Returns the current context identifier."""
        return self.current_context

    def get_curiosity_signal(self):
        """Returns the current curiosity level."""
        return self.curiosity_level

    def get_attention_signal(self):
        """Returns the current focus of attention (placeholder)."""
        return self.attention_focus

    def __repr__(self):
        return f"MS(id={self.id}, context={self.current_context}, curiosity={self.curiosity_level:.2f}, lr_mod={self.get_learning_modulation():.4f})"

