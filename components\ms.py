# -*- coding: utf-8 -*-

"""Enhanced Modulatory System (MS) for the ASCE final model with advanced neuromodulation."""

import numpy as np
import math
import random
from typing import Dict, List, Optional, Tuple, Any, Set
from collections import defaultdict, deque
from enum import Enum
from dataclasses import dataclass

class NeuromodulatorType(Enum):
    """Types of neuromodulators."""
    DOPAMINE = "dopamine"
    ACETYLCHOLINE = "acetylcholine"
    NOREPINEPHRINE = "norepinephrine"
    SEROTONIN = "serotonin"
    GABA = "gaba"
    GLUTAMATE = "glutamate"

class AttentionMode(Enum):
    """Attention modes."""
    FOCUSED = "focused"
    DISTRIBUTED = "distributed"
    EXPLORATORY = "exploratory"
    VIGILANT = "vigilant"

class MotivationalState(Enum):
    """Motivational states."""
    EXPLORATION = "exploration"
    EXPLOITATION = "exploitation"
    MAINTENANCE = "maintenance"
    EMERGENCY = "emergency"

@dataclass
class ContextState:
    """Rich context representation."""
    context_id: str
    features: Dict[str, float]
    confidence: float
    stability: float
    novelty: float
    importance: float
    emotional_valence: float

class AdvancedModulatorySystem:
    """
    Advanced Modulatory System with sophisticated neuromodulation and control.

    Features:
    - Multiple neuromodulator systems
    - Context inference and tracking
    - Attention control mechanisms
    - Curiosity and motivation systems
    - Emotional modulation
    - Homeostatic regulation
    - Predictive error monitoring
    - Goal-directed behavior
    """

    def __init__(self, ms_id: int, context_dimensions: int = 64):
        """
        Initialize the advanced modulatory system.

        Args:
            ms_id: Unique identifier
            context_dimensions: Dimensionality of context representation
        """
        self.id = ms_id
        self.context_dimensions = context_dimensions

        # Neuromodulator levels (0-1 range)
        self.neuromodulators = {
            NeuromodulatorType.DOPAMINE: 0.5,
            NeuromodulatorType.ACETYLCHOLINE: 0.5,
            NeuromodulatorType.NOREPINEPHRINE: 0.5,
            NeuromodulatorType.SEROTONIN: 0.5,
            NeuromodulatorType.GABA: 0.3,
            NeuromodulatorType.GLUTAMATE: 0.7
        }

        # Context system
        self.current_context = ContextState(
            context_id="default",
            features={},
            confidence=0.5,
            stability=0.5,
            novelty=0.0,
            importance=0.5,
            emotional_valence=0.0
        )
        self.context_history = deque(maxlen=100)
        self.context_memory = {}  # {context_id: ContextState}

        # Attention system
        self.attention_mode = AttentionMode.DISTRIBUTED
        self.attention_targets = {}  # {target_id: attention_weight}
        self.attention_focus_strength = 0.5
        self.attention_switching_threshold = 0.3

        # Curiosity and motivation
        self.curiosity_level = 0.5
        self.motivation_state = MotivationalState.EXPLORATION
        self.intrinsic_motivation = 0.5
        self.goal_stack = []  # Stack of current goals

        # Learning and adaptation
        self.base_learning_rate = 0.01
        self.learning_modulation = 1.0
        self.adaptation_rate = 0.001
        self.meta_learning_rate = 0.0001

        # Error monitoring
        self.prediction_error = 0.0
        self.surprise_level = 0.0
        self.uncertainty_level = 0.5
        self.confidence_level = 0.5

        # Emotional state
        self.emotional_valence = 0.0  # -1 (negative) to 1 (positive)
        self.emotional_arousal = 0.5  # 0 (calm) to 1 (excited)
        self.stress_level = 0.0

        # Homeostatic variables
        self.energy_level = 1.0
        self.fatigue_level = 0.0
        self.resource_availability = 1.0

        # Performance monitoring
        self.performance_history = deque(maxlen=1000)
        self.success_rate = 0.5
        self.efficiency_score = 0.5

        # Time and rhythm
        self.internal_clock = 0
        self.circadian_phase = 0.0
        self.ultradian_rhythm = 0.0

        # System parameters
        self.decay_rates = {
            "curiosity": 0.99,
            "attention": 0.95,
            "emotion": 0.98,
            "stress": 0.97,
            "fatigue": 0.999
        }

    def update_state(self, system_feedback: Dict[str, Any] = None,
                    external_signals: Dict[str, Any] = None) -> Dict[str, float]:
        """
        Update the modulatory system state based on feedback and signals.

        Args:
            system_feedback: Feedback from other system components
            external_signals: External environmental signals

        Returns:
            Dictionary of neuromodulator levels
        """
        if system_feedback is None:
            system_feedback = {}
        if external_signals is None:
            external_signals = {}

        self.internal_clock += 1

        # Update circadian and ultradian rhythms
        self._update_biological_rhythms()

        # Process system feedback
        self._process_system_feedback(system_feedback)

        # Process external signals
        self._process_external_signals(external_signals)

        # Update context
        self._update_context(system_feedback, external_signals)

        # Update attention
        self._update_attention(system_feedback)

        # Update curiosity and motivation
        self._update_curiosity_motivation(system_feedback)

        # Update emotional state
        self._update_emotional_state(system_feedback)

        # Update neuromodulator levels
        self._update_neuromodulators()

        # Apply homeostatic regulation
        self._apply_homeostasis()

        # Update performance metrics
        self._update_performance_metrics(system_feedback)

        # Apply decay to various signals
        self._apply_decay()

        return self.get_neuromodulator_levels()

    def _update_biological_rhythms(self):
        """Update internal biological rhythms."""
        # Circadian rhythm (24-hour cycle approximation)
        self.circadian_phase = (self.internal_clock % 2400) / 2400.0

        # Ultradian rhythm (90-minute cycle approximation)
        self.ultradian_rhythm = math.sin(2 * math.pi * (self.internal_clock % 90) / 90.0)

        # Modulate energy and arousal based on rhythms
        circadian_energy = 0.5 + 0.3 * math.sin(2 * math.pi * self.circadian_phase)
        self.energy_level = min(1.0, self.energy_level * 0.999 + circadian_energy * 0.001)

    def _process_system_feedback(self, feedback: Dict[str, Any]):
        """Process feedback from other system components."""
        # Prediction error
        if "prediction_error" in feedback:
            self.prediction_error = feedback["prediction_error"]
            self.surprise_level = min(1.0, self.surprise_level + self.prediction_error * 0.1)

        # Performance feedback
        if "task_success" in feedback:
            success = feedback["task_success"]
            self.success_rate = 0.9 * self.success_rate + 0.1 * (1.0 if success else 0.0)

        # Novelty detection
        if "novelty_level" in feedback:
            novelty = feedback["novelty_level"]
            self.current_context.novelty = novelty
            self.curiosity_level = min(1.0, self.curiosity_level + novelty * 0.2)

        # Uncertainty
        if "uncertainty" in feedback:
            self.uncertainty_level = feedback["uncertainty"]
            self.confidence_level = 1.0 - self.uncertainty_level

    def _process_external_signals(self, signals: Dict[str, Any]):
        """Process external environmental signals."""
        # Context cues
        if "context_cue" in signals:
            self._update_context_from_cue(signals["context_cue"])

        # Reward signals
        if "reward" in signals:
            reward = signals["reward"]
            self.emotional_valence = 0.8 * self.emotional_valence + 0.2 * reward
            # Dopamine response to reward prediction error
            expected_reward = self.emotional_valence
            reward_error = reward - expected_reward
            self.neuromodulators[NeuromodulatorType.DOPAMINE] = min(1.0,
                max(0.0, 0.5 + reward_error))

        # Stress signals
        if "stress" in signals:
            stress = signals["stress"]
            self.stress_level = min(1.0, self.stress_level + stress * 0.1)
            self.emotional_arousal = min(1.0, self.emotional_arousal + stress * 0.2)

    def _update_context_from_cue(self, context_cue: Any):
        """Update context based on external cue."""
        if isinstance(context_cue, str):
            context_id = context_cue
        elif isinstance(context_cue, dict):
            context_id = context_cue.get("id", "unknown")
        else:
            context_id = str(context_cue)

        # Check if this is a known context
        if context_id in self.context_memory:
            # Retrieve known context
            self.current_context = self.context_memory[context_id]
            self.current_context.confidence = min(1.0, self.current_context.confidence + 0.1)
        else:
            # Create new context
            self.current_context = ContextState(
                context_id=context_id,
                features={},
                confidence=0.3,
                stability=0.1,
                novelty=1.0,
                importance=0.5,
                emotional_valence=0.0
            )
            self.context_memory[context_id] = self.current_context

    def _update_context(self, system_feedback: Dict[str, Any],
                       external_signals: Dict[str, Any]):
        """Update context representation."""
        # Update context features based on feedback
        if "context_features" in system_feedback:
            features = system_feedback["context_features"]
            for key, value in features.items():
                self.current_context.features[key] = value

        # Update context stability
        if len(self.context_history) > 0:
            last_context = self.context_history[-1]
            if last_context.context_id == self.current_context.context_id:
                self.current_context.stability = min(1.0,
                    self.current_context.stability + 0.01)
            else:
                self.current_context.stability = max(0.0,
                    self.current_context.stability - 0.1)

        # Add to history
        self.context_history.append(self.current_context)

    def _update_attention(self, feedback: Dict[str, Any]):
        """Update attention mechanisms."""
        # Determine attention targets based on feedback
        if "attention_targets" in feedback:
            targets = feedback["attention_targets"]
            for target_id, importance in targets.items():
                self.attention_targets[target_id] = importance

        # Update attention mode based on context and state
        if self.uncertainty_level > 0.7:
            self.attention_mode = AttentionMode.EXPLORATORY
        elif self.stress_level > 0.6:
            self.attention_mode = AttentionMode.VIGILANT
        elif self.curiosity_level > 0.7:
            self.attention_mode = AttentionMode.EXPLORATORY
        else:
            self.attention_mode = AttentionMode.FOCUSED

        # Update attention focus strength
        if self.attention_mode == AttentionMode.FOCUSED:
            self.attention_focus_strength = min(1.0,
                self.attention_focus_strength + 0.05)
        else:
            self.attention_focus_strength = max(0.1,
                self.attention_focus_strength - 0.02)

    def _update_curiosity_motivation(self, feedback: Dict[str, Any]):
        """Update curiosity and motivational systems."""
        # Update curiosity based on novelty and uncertainty
        novelty_boost = self.current_context.novelty * 0.1
        uncertainty_boost = self.uncertainty_level * 0.05
        self.curiosity_level = min(1.0,
            self.curiosity_level + novelty_boost + uncertainty_boost)

        # Update intrinsic motivation
        if self.curiosity_level > 0.6:
            self.intrinsic_motivation = min(1.0,
                self.intrinsic_motivation + 0.02)
        else:
            self.intrinsic_motivation = max(0.1,
                self.intrinsic_motivation - 0.01)

        # Update motivational state
        if self.curiosity_level > 0.7 and self.uncertainty_level > 0.5:
            self.motivation_state = MotivationalState.EXPLORATION
        elif self.success_rate > 0.7 and self.confidence_level > 0.6:
            self.motivation_state = MotivationalState.EXPLOITATION
        elif self.stress_level > 0.8:
            self.motivation_state = MotivationalState.EMERGENCY
        else:
            self.motivation_state = MotivationalState.MAINTENANCE

    def _update_emotional_state(self, feedback: Dict[str, Any]):
        """Update emotional state."""
        # Update valence based on success/failure
        if "task_success" in feedback:
            success = feedback["task_success"]
            valence_change = 0.1 if success else -0.1
            self.emotional_valence = max(-1.0, min(1.0,
                self.emotional_valence + valence_change))

        # Update arousal based on novelty and uncertainty
        arousal_change = (self.current_context.novelty + self.uncertainty_level) * 0.05
        self.emotional_arousal = min(1.0,
            self.emotional_arousal + arousal_change)

        # Update stress based on prediction errors and failures
        if self.prediction_error > 0.5:
            self.stress_level = min(1.0, self.stress_level + 0.05)

    def _update_neuromodulators(self):
        """Update neuromodulator levels based on current state."""
        # Dopamine: reward prediction error and motivation
        dopamine_target = (0.5 + self.prediction_error * 0.3 +
                          self.intrinsic_motivation * 0.2)
        self.neuromodulators[NeuromodulatorType.DOPAMINE] = (
            0.9 * self.neuromodulators[NeuromodulatorType.DOPAMINE] +
            0.1 * dopamine_target)

        # Acetylcholine: attention and uncertainty
        ach_target = (self.attention_focus_strength * 0.5 +
                     self.uncertainty_level * 0.5)
        self.neuromodulators[NeuromodulatorType.ACETYLCHOLINE] = (
            0.9 * self.neuromodulators[NeuromodulatorType.ACETYLCHOLINE] +
            0.1 * ach_target)

        # Norepinephrine: arousal and stress
        ne_target = (self.emotional_arousal * 0.6 + self.stress_level * 0.4)
        self.neuromodulators[NeuromodulatorType.NOREPINEPHRINE] = (
            0.9 * self.neuromodulators[NeuromodulatorType.NOREPINEPHRINE] +
            0.1 * ne_target)

        # Serotonin: mood and confidence
        serotonin_target = (self.emotional_valence * 0.5 + 0.5 +
                           self.confidence_level * 0.3)
        self.neuromodulators[NeuromodulatorType.SEROTONIN] = (
            0.9 * self.neuromodulators[NeuromodulatorType.SEROTONIN] +
            0.1 * serotonin_target)

        # GABA: inhibition and stress regulation
        gaba_target = max(0.1, 0.5 - self.stress_level * 0.3)
        self.neuromodulators[NeuromodulatorType.GABA] = (
            0.9 * self.neuromodulators[NeuromodulatorType.GABA] +
            0.1 * gaba_target)

        # Glutamate: excitation and learning
        glutamate_target = min(1.0, 0.7 + self.curiosity_level * 0.2)
        self.neuromodulators[NeuromodulatorType.GLUTAMATE] = (
            0.9 * self.neuromodulators[NeuromodulatorType.GLUTAMATE] +
            0.1 * glutamate_target)

    def _apply_homeostasis(self):
        """Apply homeostatic regulation."""
        # Energy regulation
        energy_consumption = (self.emotional_arousal * 0.01 +
                            self.attention_focus_strength * 0.005)
        self.energy_level = max(0.0, self.energy_level - energy_consumption)

        # Fatigue accumulation
        fatigue_increase = (self.stress_level * 0.001 +
                          (1.0 - self.energy_level) * 0.002)
        self.fatigue_level = min(1.0, self.fatigue_level + fatigue_increase)

        # Resource availability
        self.resource_availability = min(1.0, self.energy_level * (1.0 - self.fatigue_level))

    def _update_performance_metrics(self, feedback: Dict[str, Any]):
        """Update performance tracking metrics."""
        if "efficiency" in feedback:
            efficiency = feedback["efficiency"]
            self.efficiency_score = 0.9 * self.efficiency_score + 0.1 * efficiency

        # Record performance
        performance_record = {
            "time": self.internal_clock,
            "success_rate": self.success_rate,
            "efficiency": self.efficiency_score,
            "curiosity": self.curiosity_level,
            "attention": self.attention_focus_strength
        }
        self.performance_history.append(performance_record)

    def _apply_decay(self):
        """Apply decay to various signals and states."""
        self.curiosity_level *= self.decay_rates["curiosity"]
        self.attention_focus_strength *= self.decay_rates["attention"]
        self.emotional_arousal *= self.decay_rates["emotion"]
        self.stress_level *= self.decay_rates["stress"]
        self.fatigue_level *= self.decay_rates["fatigue"]

        # Decay attention targets
        for target_id in list(self.attention_targets.keys()):
            self.attention_targets[target_id] *= 0.95
            if self.attention_targets[target_id] < 0.1:
                del self.attention_targets[target_id]

    def get_neuromodulator_levels(self) -> Dict[str, float]:
        """Get current neuromodulator levels."""
        return {nt.value: level for nt, level in self.neuromodulators.items()}

    def get_learning_modulation(self) -> float:
        """Calculate learning rate modulation."""
        # Base modulation from curiosity and attention
        curiosity_factor = 0.5 + self.curiosity_level * 0.5
        attention_factor = 0.5 + self.attention_focus_strength * 0.3

        # Neuromodulator influences
        dopamine_factor = self.neuromodulators[NeuromodulatorType.DOPAMINE]
        acetylcholine_factor = self.neuromodulators[NeuromodulatorType.ACETYLCHOLINE]

        # Combine factors
        modulation = (self.base_learning_rate * curiosity_factor *
                     attention_factor * dopamine_factor * acetylcholine_factor)

        # Apply resource constraints
        modulation *= self.resource_availability

        return modulation

    def get_context_signal(self) -> Dict[str, Any]:
        """Get rich context signal."""
        return {
            "context_id": self.current_context.context_id,
            "features": self.current_context.features.copy(),
            "confidence": self.current_context.confidence,
            "stability": self.current_context.stability,
            "novelty": self.current_context.novelty,
            "importance": self.current_context.importance,
            "emotional_valence": self.current_context.emotional_valence
        }

    def get_attention_signal(self) -> Dict[str, Any]:
        """Get attention control signal."""
        return {
            "mode": self.attention_mode.value,
            "focus_strength": self.attention_focus_strength,
            "targets": self.attention_targets.copy(),
            "switching_threshold": self.attention_switching_threshold
        }

    def get_curiosity_signal(self) -> float:
        """Get curiosity level."""
        return self.curiosity_level

    def get_motivation_signal(self) -> Dict[str, Any]:
        """Get motivational state signal."""
        return {
            "state": self.motivation_state.value,
            "intrinsic_motivation": self.intrinsic_motivation,
            "goal_stack": self.goal_stack.copy(),
            "energy_level": self.energy_level,
            "fatigue_level": self.fatigue_level
        }

    def get_emotional_signal(self) -> Dict[str, float]:
        """Get emotional state signal."""
        return {
            "valence": self.emotional_valence,
            "arousal": self.emotional_arousal,
            "stress": self.stress_level,
            "confidence": self.confidence_level
        }

    def set_goal(self, goal: Dict[str, Any], priority: int = 1):
        """Set a new goal for the system."""
        goal_entry = {
            "goal": goal,
            "priority": priority,
            "creation_time": self.internal_clock,
            "progress": 0.0
        }

        # Insert based on priority
        inserted = False
        for i, existing_goal in enumerate(self.goal_stack):
            if priority > existing_goal["priority"]:
                self.goal_stack.insert(i, goal_entry)
                inserted = True
                break

        if not inserted:
            self.goal_stack.append(goal_entry)

        # Limit goal stack size
        if len(self.goal_stack) > 10:
            self.goal_stack = self.goal_stack[:10]

    def update_goal_progress(self, goal_index: int, progress: float):
        """Update progress on a specific goal."""
        if 0 <= goal_index < len(self.goal_stack):
            self.goal_stack[goal_index]["progress"] = progress

            # Remove completed goals
            if progress >= 1.0:
                completed_goal = self.goal_stack.pop(goal_index)
                # Reward for goal completion
                self.emotional_valence = min(1.0, self.emotional_valence + 0.1)

    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        return {
            "ms_id": self.id,
            "internal_clock": self.internal_clock,
            "neuromodulators": self.get_neuromodulator_levels(),
            "context": self.get_context_signal(),
            "attention": self.get_attention_signal(),
            "motivation": self.get_motivation_signal(),
            "emotion": self.get_emotional_signal(),
            "performance": {
                "success_rate": self.success_rate,
                "efficiency_score": self.efficiency_score,
                "prediction_error": self.prediction_error,
                "uncertainty_level": self.uncertainty_level
            },
            "homeostasis": {
                "energy_level": self.energy_level,
                "fatigue_level": self.fatigue_level,
                "resource_availability": self.resource_availability,
                "stress_level": self.stress_level
            },
            "rhythms": {
                "circadian_phase": self.circadian_phase,
                "ultradian_rhythm": self.ultradian_rhythm
            }
        }

    def save_state(self) -> Dict[str, Any]:
        """Save complete MS state."""
        return {
            "ms_id": self.id,
            "neuromodulators": {nt.value: level for nt, level in self.neuromodulators.items()},
            "context": {
                "current": {
                    "context_id": self.current_context.context_id,
                    "features": self.current_context.features,
                    "confidence": self.current_context.confidence,
                    "stability": self.current_context.stability,
                    "novelty": self.current_context.novelty,
                    "importance": self.current_context.importance,
                    "emotional_valence": self.current_context.emotional_valence
                },
                "memory": {cid: {
                    "context_id": ctx.context_id,
                    "features": ctx.features,
                    "confidence": ctx.confidence,
                    "stability": ctx.stability,
                    "novelty": ctx.novelty,
                    "importance": ctx.importance,
                    "emotional_valence": ctx.emotional_valence
                } for cid, ctx in self.context_memory.items()}
            },
            "attention": {
                "mode": self.attention_mode.value,
                "targets": self.attention_targets,
                "focus_strength": self.attention_focus_strength
            },
            "motivation": {
                "state": self.motivation_state.value,
                "curiosity_level": self.curiosity_level,
                "intrinsic_motivation": self.intrinsic_motivation,
                "goal_stack": self.goal_stack
            },
            "emotion": {
                "valence": self.emotional_valence,
                "arousal": self.emotional_arousal,
                "stress": self.stress_level
            },
            "performance": {
                "success_rate": self.success_rate,
                "efficiency_score": self.efficiency_score,
                "prediction_error": self.prediction_error
            },
            "internal_state": {
                "internal_clock": self.internal_clock,
                "energy_level": self.energy_level,
                "fatigue_level": self.fatigue_level,
                "circadian_phase": self.circadian_phase
            }
        }

    def reset_state(self):
        """Reset MS to initial state."""
        # Reset neuromodulators
        for nt in self.neuromodulators:
            if nt == NeuromodulatorType.GABA:
                self.neuromodulators[nt] = 0.3
            elif nt == NeuromodulatorType.GLUTAMATE:
                self.neuromodulators[nt] = 0.7
            else:
                self.neuromodulators[nt] = 0.5

        # Reset context
        self.current_context = ContextState(
            context_id="default",
            features={},
            confidence=0.5,
            stability=0.5,
            novelty=0.0,
            importance=0.5,
            emotional_valence=0.0
        )
        self.context_history.clear()
        self.context_memory.clear()

        # Reset other systems
        self.attention_mode = AttentionMode.DISTRIBUTED
        self.attention_targets.clear()
        self.curiosity_level = 0.5
        self.motivation_state = MotivationalState.EXPLORATION
        self.goal_stack.clear()

        # Reset performance
        self.performance_history.clear()
        self.success_rate = 0.5
        self.efficiency_score = 0.5

        # Reset time
        self.internal_clock = 0
        self.circadian_phase = 0.0

    def __repr__(self):
        stats = self.get_system_statistics()
        return (f"AdvancedModulatorySystem(id={self.id}, "
                f"context={stats['context']['context_id']}, "
                f"curiosity={self.curiosity_level:.3f}, "
                f"attention={self.attention_mode.value}, "
                f"motivation={self.motivation_state.value})")


# Backward compatibility class
class ModulatorySystem(AdvancedModulatorySystem):
    """Backward compatible modulatory system class."""

    def __init__(self, ms_id):
        super().__init__(ms_id)

        # Legacy attributes for backward compatibility
        self.current_context = 0
        self.base_learning_rate = 0.01
        self.attention_focus = None

    def update_state(self, system_error=None, novelty_signal=None, external_context_cue=None):
        """Update state with backward compatibility."""
        # Convert legacy parameters to new format
        system_feedback = {}
        external_signals = {}

        if system_error is not None:
            system_feedback["prediction_error"] = system_error
            system_feedback["task_success"] = system_error < 0.5

        if novelty_signal is not None:
            system_feedback["novelty_level"] = novelty_signal

        if external_context_cue is not None:
            external_signals["context_cue"] = external_context_cue
            self.current_context = external_context_cue

        # Call advanced update
        super().update_state(system_feedback, external_signals)

        # Update legacy attributes
        self.attention_focus = list(self.attention_targets.keys())[0] if self.attention_targets else None

    def get_learning_modulation(self):
        """Get learning modulation with backward compatibility."""
        return super().get_learning_modulation()

    def get_context_signal(self):
        """Get context signal with backward compatibility."""
        return self.current_context

    def get_curiosity_signal(self):
        """Get curiosity signal with backward compatibility."""
        return super().get_curiosity_signal()

    def get_attention_signal(self):
        """Get attention signal with backward compatibility."""
        return self.attention_focus

