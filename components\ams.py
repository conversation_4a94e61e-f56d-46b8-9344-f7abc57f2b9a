# -*- coding: utf-8 -*-

"""Enhanced Abstract Memory System (AMS) for the ASCE final model with advanced capabilities."""

import numpy as np
import math
import random
from typing import Dict, List, Optional, Tuple, Any, Set
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum

class MemoryType(Enum):
    """Types of memory traces."""
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    PROCEDURAL = "procedural"
    WORKING = "working"
    ABSTRACT = "abstract"

class ConsolidationLevel(Enum):
    """Levels of memory consolidation."""
    FRESH = "fresh"
    CONSOLIDATING = "consolidating"
    CONSOLIDATED = "consolidated"
    CRYSTALLIZED = "crystallized"

@dataclass
class MemoryTrace:
    """Enhanced memory trace with rich metadata."""
    pattern: np.ndarray
    memory_type: MemoryType
    consolidation_level: ConsolidationLevel
    strength: float
    creation_time: int
    last_accessed: int
    access_count: int
    context: Dict[str, Any]
    associations: Set[int]  # IDs of associated traces
    importance: float
    surprise: float
    emotional_valence: float

    def __post_init__(self):
        if self.associations is None:
            self.associations = set()

class AdvancedAMS:
    """
    Advanced Abstract Memory System with sophisticated memory mechanisms.

    Features:
    - Multiple memory types (episodic, semantic, procedural)
    - Hierarchical memory organization
    - Dynamic consolidation and forgetting
    - Associative memory networks
    - Context-dependent retrieval
    - Emotional modulation
    - Compression and abstraction
    - Memory replay and rehearsal
    """

    def __init__(self, ams_id: int, representation_size: int,
                 max_traces: int = 10000, consolidation_threshold: float = 0.8):
        """
        Initialize the advanced AMS.

        Args:
            ams_id: Unique identifier
            representation_size: Size of memory representations
            max_traces: Maximum number of memory traces
            consolidation_threshold: Threshold for memory consolidation
        """
        self.id = ams_id
        self.representation_size = representation_size
        self.max_traces = max_traces
        self.consolidation_threshold = consolidation_threshold

        # Memory storage
        self.memory_traces = {}  # {trace_id: MemoryTrace}
        self.trace_index = {}    # {pattern_hash: trace_id}
        self.context_index = defaultdict(set)  # {context_key: set of trace_ids}
        self.association_graph = defaultdict(set)  # {trace_id: set of associated_ids}

        # Memory organization
        self.memory_hierarchy = {
            MemoryType.EPISODIC: {},
            MemoryType.SEMANTIC: {},
            MemoryType.PROCEDURAL: {},
            MemoryType.WORKING: {},
            MemoryType.ABSTRACT: {}
        }

        # System state
        self.current_time = 0
        self.next_trace_id = 0
        self.consolidation_queue = deque()
        self.forgetting_queue = deque()

        # Learning parameters
        self.learning_rate = 0.01
        self.forgetting_rate = 0.001
        self.association_threshold = 0.7
        self.compression_ratio = 0.5

        # Retrieval parameters
        self.retrieval_noise = 0.1
        self.context_weight = 0.3
        self.recency_weight = 0.2
        self.frequency_weight = 0.3
        self.importance_weight = 0.2

        # Statistics
        self.total_stores = 0
        self.total_retrievals = 0
        self.consolidation_events = 0
        self.forgetting_events = 0

        # Emotional and motivational state
        self.emotional_state = {"valence": 0.0, "arousal": 0.5}
        self.curiosity_level = 0.5
        self.attention_focus = None

    def store_pattern(self, pattern: np.ndarray, memory_type: MemoryType = MemoryType.EPISODIC,
                     context: Dict[str, Any] = None, importance: float = 0.5,
                     emotional_valence: float = 0.0) -> int:
        """
        Store a pattern in memory with rich metadata.

        Args:
            pattern: Pattern to store
            memory_type: Type of memory
            context: Contextual information
            importance: Importance score (0-1)
            emotional_valence: Emotional valence (-1 to 1)

        Returns:
            Trace ID of stored pattern
        """
        if len(pattern) != self.representation_size:
            raise ValueError(f"Pattern size mismatch: {len(pattern)} != {self.representation_size}")

        if context is None:
            context = {}

        # Check for existing similar patterns
        pattern_hash = self._hash_pattern(pattern)
        if pattern_hash in self.trace_index:
            # Update existing trace
            trace_id = self.trace_index[pattern_hash]
            self._update_existing_trace(trace_id, context, importance)
            return trace_id

        # Create new trace
        trace_id = self.next_trace_id
        self.next_trace_id += 1

        # Calculate surprise based on novelty
        surprise = self._calculate_surprise(pattern, context)

        # Create memory trace
        trace = MemoryTrace(
            pattern=pattern.copy(),
            memory_type=memory_type,
            consolidation_level=ConsolidationLevel.FRESH,
            strength=importance + surprise * 0.3,
            creation_time=self.current_time,
            last_accessed=self.current_time,
            access_count=1,
            context=context.copy(),
            associations=set(),
            importance=importance,
            surprise=surprise,
            emotional_valence=emotional_valence
        )

        # Store trace
        self.memory_traces[trace_id] = trace
        self.trace_index[pattern_hash] = trace_id
        self.memory_hierarchy[memory_type][trace_id] = trace

        # Index by context
        for key, value in context.items():
            context_key = f"{key}:{value}"
            self.context_index[context_key].add(trace_id)

        # Find and create associations
        self._create_associations(trace_id, pattern, context)

        # Add to consolidation queue if important
        if trace.strength > self.consolidation_threshold:
            self.consolidation_queue.append(trace_id)

        # Manage memory capacity
        if len(self.memory_traces) > self.max_traces:
            self._manage_memory_capacity()

        self.total_stores += 1
        self.current_time += 1

        return trace_id

    def _hash_pattern(self, pattern: np.ndarray) -> str:
        """Create a hash for pattern matching."""
        # Use sparse representation for hashing
        active_indices = np.where(pattern > 0.1)[0]
        return ",".join(map(str, active_indices))

    def _calculate_surprise(self, pattern: np.ndarray, context: Dict[str, Any]) -> float:
        """Calculate surprise/novelty of a pattern."""
        if not self.memory_traces:
            return 1.0  # Maximum surprise for first pattern

        # Find similar patterns
        similarities = []
        for trace in self.memory_traces.values():
            similarity = self._calculate_similarity(pattern, trace.pattern)
            similarities.append(similarity)

        # Surprise is inverse of maximum similarity
        max_similarity = max(similarities) if similarities else 0.0
        surprise = 1.0 - max_similarity

        # Modulate by context novelty
        context_novelty = self._calculate_context_novelty(context)
        surprise = 0.7 * surprise + 0.3 * context_novelty

        return surprise

    def _calculate_context_novelty(self, context: Dict[str, Any]) -> float:
        """Calculate novelty of context."""
        if not context:
            return 0.0

        novelty_scores = []
        for key, value in context.items():
            context_key = f"{key}:{value}"
            if context_key in self.context_index:
                # Familiar context
                frequency = len(self.context_index[context_key])
                novelty = 1.0 / (1.0 + frequency)
            else:
                # Novel context
                novelty = 1.0
            novelty_scores.append(novelty)

        return np.mean(novelty_scores) if novelty_scores else 0.0

    def _update_existing_trace(self, trace_id: int, context: Dict[str, Any],
                              importance: float):
        """Update an existing memory trace."""
        trace = self.memory_traces[trace_id]

        # Update access information
        trace.last_accessed = self.current_time
        trace.access_count += 1

        # Update strength based on repeated exposure
        trace.strength = min(1.0, trace.strength + 0.1)

        # Update importance
        trace.importance = max(trace.importance, importance)

        # Merge context information
        for key, value in context.items():
            if key not in trace.context:
                trace.context[key] = value
                context_key = f"{key}:{value}"
                self.context_index[context_key].add(trace_id)

    def _create_associations(self, trace_id: int, pattern: np.ndarray,
                           context: Dict[str, Any]):
        """Create associations with existing memory traces."""
        trace = self.memory_traces[trace_id]

        # Find similar patterns for association
        for other_id, other_trace in self.memory_traces.items():
            if other_id == trace_id:
                continue

            # Calculate pattern similarity
            pattern_sim = self._calculate_similarity(pattern, other_trace.pattern)

            # Calculate context similarity
            context_sim = self._calculate_context_similarity(context, other_trace.context)

            # Combined similarity
            total_sim = 0.7 * pattern_sim + 0.3 * context_sim

            if total_sim > self.association_threshold:
                # Create bidirectional association
                trace.associations.add(other_id)
                other_trace.associations.add(trace_id)
                self.association_graph[trace_id].add(other_id)
                self.association_graph[other_id].add(trace_id)

    def _calculate_similarity(self, pattern1: np.ndarray, pattern2: np.ndarray) -> float:
        """Calculate similarity between two patterns."""
        # Cosine similarity
        dot_product = np.dot(pattern1, pattern2)
        norm1 = np.linalg.norm(pattern1)
        norm2 = np.linalg.norm(pattern2)

        if norm1 > 0 and norm2 > 0:
            return dot_product / (norm1 * norm2)
        return 0.0

    def _calculate_context_similarity(self, context1: Dict[str, Any],
                                    context2: Dict[str, Any]) -> float:
        """Calculate similarity between contexts."""
        if not context1 or not context2:
            return 0.0

        # Jaccard similarity of context keys and values
        set1 = set(f"{k}:{v}" for k, v in context1.items())
        set2 = set(f"{k}:{v}" for k, v in context2.items())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def retrieve_similar(self, cue_pattern: np.ndarray, top_k: int = 5,
                        similarity_threshold: float = 0.3,
                        context: Dict[str, Any] = None,
                        memory_types: List[MemoryType] = None) -> List[Tuple[float, MemoryTrace]]:
        """
        Retrieve similar patterns with advanced scoring.

        Args:
            cue_pattern: Pattern to match
            top_k: Number of results to return
            similarity_threshold: Minimum similarity threshold
            context: Current context for context-dependent retrieval
            memory_types: Types of memory to search

        Returns:
            List of (score, trace) tuples sorted by relevance
        """
        if len(cue_pattern) != self.representation_size:
            raise ValueError(f"Cue pattern size mismatch")

        if context is None:
            context = {}

        if memory_types is None:
            memory_types = list(MemoryType)

        candidates = []

        # Collect candidate traces
        for memory_type in memory_types:
            for trace_id, trace in self.memory_hierarchy[memory_type].items():
                candidates.append((trace_id, trace))

        if not candidates:
            return []

        # Score each candidate
        scored_candidates = []
        for trace_id, trace in candidates:
            score = self._calculate_retrieval_score(
                cue_pattern, trace, context
            )

            if score >= similarity_threshold:
                scored_candidates.append((score, trace))

        # Sort by score and return top_k
        scored_candidates.sort(key=lambda x: x[0], reverse=True)

        # Update access information for retrieved traces
        for score, trace in scored_candidates[:top_k]:
            trace.last_accessed = self.current_time
            trace.access_count += 1

        self.total_retrievals += 1
        self.current_time += 1

        return scored_candidates[:top_k]

    def _calculate_retrieval_score(self, cue_pattern: np.ndarray,
                                  trace: MemoryTrace, context: Dict[str, Any]) -> float:
        """Calculate comprehensive retrieval score."""
        # Pattern similarity
        pattern_sim = self._calculate_similarity(cue_pattern, trace.pattern)

        # Context similarity
        context_sim = self._calculate_context_similarity(context, trace.context)

        # Recency factor (more recent = higher score)
        time_diff = max(1, self.current_time - trace.last_accessed)
        recency_factor = 1.0 / math.log(1 + time_diff)

        # Frequency factor (more accessed = higher score)
        frequency_factor = math.log(1 + trace.access_count)

        # Importance and strength factors
        importance_factor = trace.importance
        strength_factor = trace.strength

        # Emotional modulation
        emotional_factor = 1.0 + abs(trace.emotional_valence) * 0.2

        # Combine factors
        score = (pattern_sim * (1.0 - self.context_weight) +
                context_sim * self.context_weight +
                recency_factor * self.recency_weight +
                frequency_factor * self.frequency_weight +
                importance_factor * self.importance_weight) * emotional_factor

        return score

    def consolidate(self, max_consolidations: int = 10) -> int:
        """
        Perform memory consolidation.

        Args:
            max_consolidations: Maximum number of traces to consolidate

        Returns:
            Number of traces consolidated
        """
        consolidated_count = 0

        while (self.consolidation_queue and
               consolidated_count < max_consolidations):

            trace_id = self.consolidation_queue.popleft()
            if trace_id not in self.memory_traces:
                continue

            trace = self.memory_traces[trace_id]

            # Check if trace is ready for consolidation
            if (trace.strength > self.consolidation_threshold and
                trace.consolidation_level == ConsolidationLevel.FRESH):

                # Perform consolidation
                self._consolidate_trace(trace)
                consolidated_count += 1
                self.consolidation_events += 1

        return consolidated_count

    def _consolidate_trace(self, trace: MemoryTrace):
        """Consolidate a single memory trace."""
        # Update consolidation level
        if trace.consolidation_level == ConsolidationLevel.FRESH:
            trace.consolidation_level = ConsolidationLevel.CONSOLIDATING
        elif trace.consolidation_level == ConsolidationLevel.CONSOLIDATING:
            trace.consolidation_level = ConsolidationLevel.CONSOLIDATED
        elif trace.consolidation_level == ConsolidationLevel.CONSOLIDATED:
            trace.consolidation_level = ConsolidationLevel.CRYSTALLIZED

        # Strengthen the trace
        trace.strength = min(1.0, trace.strength * 1.1)

        # Compress pattern if highly consolidated
        if trace.consolidation_level == ConsolidationLevel.CRYSTALLIZED:
            trace.pattern = self._compress_pattern(trace.pattern)

        # Strengthen associations
        for assoc_id in trace.associations:
            if assoc_id in self.memory_traces:
                assoc_trace = self.memory_traces[assoc_id]
                assoc_trace.strength = min(1.0, assoc_trace.strength + 0.05)

    def _compress_pattern(self, pattern: np.ndarray) -> np.ndarray:
        """Compress a pattern while preserving important features."""
        # Simple compression: keep only the strongest activations
        threshold = np.percentile(pattern, 100 * (1 - self.compression_ratio))
        compressed = pattern.copy()
        compressed[compressed < threshold] = 0
        return compressed

