# -*- coding: utf-8 -*-

"""Implements a simplified Abstract Memory System (AMS) for the ASCE prototype."""

import numpy as np

class AMS:
    """Represents a basic Abstract Memory System.

    Stores abstract patterns (e.g., sparse codes or representative activity) and allows
    retrieval based on similarity to input cues.
    """
    def __init__(self, ams_id, representation_size):
        """Initializes the AMS.

        Args:
            ams_id: A unique identifier for this AMS.
            representation_size: The dimensionality of the stored abstract representations.
                                 (Could correspond to a subset of CPU neurons or a dedicated space).
        """
        self.id = ams_id
        self.representation_size = representation_size
        self.memory_traces = [] # List to store learned abstract patterns/representations
        # Each trace could be a dictionary: {"pattern": sparse_vector, "metadata": {...}}

    def store_pattern(self, pattern, metadata=None):
        """Stores a new abstract pattern representation in memory.

        Args:
            pattern: The abstract pattern to store (e.g., a sparse numpy array or list of active indices).
            metadata: Optional dictionary for associated info (e.g., context, source CPU).
        """
        if len(pattern) != self.representation_size:
             # Simple check, could be more sophisticated (e.g., checking sparsity)
            raise ValueError(f"Pattern size ({len(pattern)}) does not match AMS representation size ({self.representation_size}).")

        # Basic storage - could add checks for duplicates or consolidation logic later
        trace = {"pattern": np.array(pattern), "metadata": metadata or {}}
        self.memory_traces.append(trace)
        # print(f"AMS {self.id}: Stored pattern. Total traces: {len(self.memory_traces)}")

    def retrieve_similar(self, cue_pattern, top_k=1, similarity_threshold=0.5):
        """Retrieves the most similar stored patterns based on a cue.

        Args:
            cue_pattern: The input pattern to match against stored traces.
            top_k: The maximum number of similar patterns to return.
            similarity_threshold: Minimum similarity score required for retrieval.

        Returns:
            A list of tuples [(similarity_score, retrieved_trace), ...], sorted by similarity.
        """
        if len(cue_pattern) != self.representation_size:
            raise ValueError(f"Cue pattern size ({len(cue_pattern)}) does not match AMS representation size ({self.representation_size}).")

        if not self.memory_traces:
            return []

        cue_vec = np.array(cue_pattern)
        similarities = []

        for trace in self.memory_traces:
            # Using cosine similarity as an example metric for sparse vectors
            stored_vec = trace["pattern"]
            dot_product = np.dot(cue_vec, stored_vec)
            norm_cue = np.linalg.norm(cue_vec)
            norm_stored = np.linalg.norm(stored_vec)

            if norm_cue > 0 and norm_stored > 0:
                similarity = dot_product / (norm_cue * norm_stored)
            else:
                similarity = 0.0

            if similarity >= similarity_threshold:
                similarities.append((similarity, trace))

        # Sort by similarity (descending) and return top_k
        similarities.sort(key=lambda x: x[0], reverse=True)
        return similarities[:top_k]

    def consolidate(self):
        """Placeholder for future consolidation logic.

        Could involve merging similar traces, strengthening common patterns,
        or interacting with AFA (Adaptive Forgetting Algorithm).
        """
        # print(f"AMS {self.id}: Consolidation step (placeholder).")
        pass

    def __repr__(self):
        return f"AMS(id={self.id}, traces={len(self.memory_traces)}, size={self.representation_size})"

