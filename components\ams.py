# -*- coding: utf-8 -*-

"""Enhanced Abstract Memory System (AMS) for the ASCE final model with advanced capabilities."""

import numpy as np
import math
import random
from typing import Dict, List, Optional, Tuple, Any, Set
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum

class MemoryType(Enum):
    """Types of memory traces."""
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    PROCEDURAL = "procedural"
    WORKING = "working"
    ABSTRACT = "abstract"

class ConsolidationLevel(Enum):
    """Levels of memory consolidation."""
    FRESH = "fresh"
    CONSOLIDATING = "consolidating"
    CONSOLIDATED = "consolidated"
    CRYSTALLIZED = "crystallized"

@dataclass
class MemoryTrace:
    """Enhanced memory trace with rich metadata."""
    pattern: np.ndarray
    memory_type: MemoryType
    consolidation_level: ConsolidationLevel
    strength: float
    creation_time: int
    last_accessed: int
    access_count: int
    context: Dict[str, Any]
    associations: Set[int]  # IDs of associated traces
    importance: float
    surprise: float
    emotional_valence: float

    def __post_init__(self):
        if self.associations is None:
            self.associations = set()

class AdvancedAMS:
    """
    Advanced Abstract Memory System with sophisticated memory mechanisms.

    Features:
    - Multiple memory types (episodic, semantic, procedural)
    - Hierarchical memory organization
    - Dynamic consolidation and forgetting
    - Associative memory networks
    - Context-dependent retrieval
    - Emotional modulation
    - Compression and abstraction
    - Memory replay and rehearsal
    """

    def __init__(self, ams_id: int, representation_size: int,
                 max_traces: int = 10000, consolidation_threshold: float = 0.8):
        """
        Initialize the advanced AMS.

        Args:
            ams_id: Unique identifier
            representation_size: Size of memory representations
            max_traces: Maximum number of memory traces
            consolidation_threshold: Threshold for memory consolidation
        """
        self.id = ams_id
        self.representation_size = representation_size
        self.max_traces = max_traces
        self.consolidation_threshold = consolidation_threshold

        # Memory storage
        self.memory_traces = {}  # {trace_id: MemoryTrace}
        self.trace_index = {}    # {pattern_hash: trace_id}
        self.context_index = defaultdict(set)  # {context_key: set of trace_ids}
        self.association_graph = defaultdict(set)  # {trace_id: set of associated_ids}

        # Memory organization
        self.memory_hierarchy = {
            MemoryType.EPISODIC: {},
            MemoryType.SEMANTIC: {},
            MemoryType.PROCEDURAL: {},
            MemoryType.WORKING: {},
            MemoryType.ABSTRACT: {}
        }

        # System state
        self.current_time = 0
        self.next_trace_id = 0
        self.consolidation_queue = deque()
        self.forgetting_queue = deque()

        # Learning parameters
        self.learning_rate = 0.01
        self.forgetting_rate = 0.001
        self.association_threshold = 0.7
        self.compression_ratio = 0.5

        # Retrieval parameters
        self.retrieval_noise = 0.1
        self.context_weight = 0.3
        self.recency_weight = 0.2
        self.frequency_weight = 0.3
        self.importance_weight = 0.2

        # Statistics
        self.total_stores = 0
        self.total_retrievals = 0
        self.consolidation_events = 0
        self.forgetting_events = 0

        # Emotional and motivational state
        self.emotional_state = {"valence": 0.0, "arousal": 0.5}
        self.curiosity_level = 0.5
        self.attention_focus = None

    def store_pattern(self, pattern: np.ndarray, memory_type: MemoryType = MemoryType.EPISODIC,
                     context: Dict[str, Any] = None, importance: float = 0.5,
                     emotional_valence: float = 0.0) -> int:
        """
        Store a pattern in memory with rich metadata.

        Args:
            pattern: Pattern to store
            memory_type: Type of memory
            context: Contextual information
            importance: Importance score (0-1)
            emotional_valence: Emotional valence (-1 to 1)

        Returns:
            Trace ID of stored pattern
        """
        if len(pattern) != self.representation_size:
            raise ValueError(f"Pattern size mismatch: {len(pattern)} != {self.representation_size}")

        if context is None:
            context = {}

        # Check for existing similar patterns
        pattern_hash = self._hash_pattern(pattern)
        if pattern_hash in self.trace_index:
            # Update existing trace
            trace_id = self.trace_index[pattern_hash]
            self._update_existing_trace(trace_id, context, importance)
            return trace_id

        # Create new trace
        trace_id = self.next_trace_id
        self.next_trace_id += 1

        # Calculate surprise based on novelty
        surprise = self._calculate_surprise(pattern, context)

        # Create memory trace
        trace = MemoryTrace(
            pattern=pattern.copy(),
            memory_type=memory_type,
            consolidation_level=ConsolidationLevel.FRESH,
            strength=importance + surprise * 0.3,
            creation_time=self.current_time,
            last_accessed=self.current_time,
            access_count=1,
            context=context.copy(),
            associations=set(),
            importance=importance,
            surprise=surprise,
            emotional_valence=emotional_valence
        )

        # Store trace
        self.memory_traces[trace_id] = trace
        self.trace_index[pattern_hash] = trace_id
        self.memory_hierarchy[memory_type][trace_id] = trace

        # Index by context
        for key, value in context.items():
            context_key = f"{key}:{value}"
            self.context_index[context_key].add(trace_id)

        # Find and create associations
        self._create_associations(trace_id, pattern, context)

        # Add to consolidation queue if important
        if trace.strength > self.consolidation_threshold:
            self.consolidation_queue.append(trace_id)

        # Manage memory capacity
        if len(self.memory_traces) > self.max_traces:
            self._manage_memory_capacity()

        self.total_stores += 1
        self.current_time += 1

        return trace_id

    def _hash_pattern(self, pattern: np.ndarray) -> str:
        """Create a hash for pattern matching."""
        # Use sparse representation for hashing
        active_indices = np.where(pattern > 0.1)[0]
        return ",".join(map(str, active_indices))

    def _calculate_surprise(self, pattern: np.ndarray, context: Dict[str, Any]) -> float:
        """Calculate surprise/novelty of a pattern."""
        if not self.memory_traces:
            return 1.0  # Maximum surprise for first pattern

        # Find similar patterns
        similarities = []
        for trace in self.memory_traces.values():
            similarity = self._calculate_similarity(pattern, trace.pattern)
            similarities.append(similarity)

        # Surprise is inverse of maximum similarity
        max_similarity = max(similarities) if similarities else 0.0
        surprise = 1.0 - max_similarity

        # Modulate by context novelty
        context_novelty = self._calculate_context_novelty(context)
        surprise = 0.7 * surprise + 0.3 * context_novelty

        return surprise

    def _calculate_context_novelty(self, context: Dict[str, Any]) -> float:
        """Calculate novelty of context."""
        if not context:
            return 0.0

        novelty_scores = []
        for key, value in context.items():
            context_key = f"{key}:{value}"
            if context_key in self.context_index:
                # Familiar context
                frequency = len(self.context_index[context_key])
                novelty = 1.0 / (1.0 + frequency)
            else:
                # Novel context
                novelty = 1.0
            novelty_scores.append(novelty)

        return np.mean(novelty_scores) if novelty_scores else 0.0

    def _create_associations(self, trace_id: int, pattern: np.ndarray,
                           context: Dict[str, Any]):
        """Create associations with existing memory traces."""
        trace = self.memory_traces[trace_id]

        # Find similar patterns for association
        for other_id, other_trace in self.memory_traces.items():
            if other_id == trace_id:
                continue

            # Calculate pattern similarity
            pattern_sim = self._calculate_similarity(pattern, other_trace.pattern)

            # Calculate context similarity
            context_sim = self._calculate_context_similarity(context, other_trace.context)

            # Combined similarity
            total_sim = 0.7 * pattern_sim + 0.3 * context_sim

            if total_sim > self.association_threshold:
                # Create bidirectional association
                trace.associations.add(other_id)
                other_trace.associations.add(trace_id)
                self.association_graph[trace_id].add(other_id)
                self.association_graph[other_id].add(trace_id)

    def _calculate_similarity(self, pattern1: np.ndarray, pattern2: np.ndarray) -> float:
        """Calculate similarity between two patterns."""
        # Cosine similarity
        dot_product = np.dot(pattern1, pattern2)
        norm1 = np.linalg.norm(pattern1)
        norm2 = np.linalg.norm(pattern2)

        if norm1 > 0 and norm2 > 0:
            return dot_product / (norm1 * norm2)
        return 0.0

    def _calculate_context_similarity(self, context1: Dict[str, Any],
                                    context2: Dict[str, Any]) -> float:
        """Calculate similarity between contexts."""
        if not context1 or not context2:
            return 0.0

        # Jaccard similarity of context keys and values
        set1 = set(f"{k}:{v}" for k, v in context1.items())
        set2 = set(f"{k}:{v}" for k, v in context2.items())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _update_existing_trace(self, trace_id: int, context: Dict[str, Any],
                              importance: float):
        """Update an existing memory trace."""
        trace = self.memory_traces[trace_id]

        # Update access information
        trace.last_accessed = self.current_time
        trace.access_count += 1

        # Update strength based on repeated exposure
        trace.strength = min(1.0, trace.strength + 0.1)

        # Update importance
        trace.importance = max(trace.importance, importance)

        # Merge context information
        for key, value in context.items():
            if key not in trace.context:
                trace.context[key] = value
                context_key = f"{key}:{value}"
                self.context_index[context_key].add(trace_id)

    def retrieve_similar(self, cue_pattern: np.ndarray, top_k: int = 5,
                        similarity_threshold: float = 0.3,
                        context: Dict[str, Any] = None) -> List[Tuple[float, MemoryTrace]]:
        """
        Retrieve similar patterns with advanced scoring.

        Args:
            cue_pattern: Pattern to match
            top_k: Number of results to return
            similarity_threshold: Minimum similarity threshold
            context: Current context for context-dependent retrieval

        Returns:
            List of (score, trace) tuples sorted by relevance
        """
        if len(cue_pattern) != self.representation_size:
            raise ValueError(f"Cue pattern size mismatch")

        if context is None:
            context = {}

        if not self.memory_traces:
            return []

        # Score each trace
        scored_traces = []
        for trace_id, trace in self.memory_traces.items():
            score = self._calculate_retrieval_score(cue_pattern, trace, context)

            if score >= similarity_threshold:
                scored_traces.append((score, trace))

        # Sort by score and return top_k
        scored_traces.sort(key=lambda x: x[0], reverse=True)

        # Update access information for retrieved traces
        for score, trace in scored_traces[:top_k]:
            trace.last_accessed = self.current_time
            trace.access_count += 1

        self.total_retrievals += 1
        self.current_time += 1

        return scored_traces[:top_k]

    def _calculate_retrieval_score(self, cue_pattern: np.ndarray,
                                  trace: MemoryTrace, context: Dict[str, Any]) -> float:
        """Calculate comprehensive retrieval score."""
        # Pattern similarity (cosine similarity)
        dot_product = np.dot(cue_pattern, trace.pattern)
        norm_cue = np.linalg.norm(cue_pattern)
        norm_stored = np.linalg.norm(trace.pattern)

        if norm_cue > 0 and norm_stored > 0:
            pattern_sim = dot_product / (norm_cue * norm_stored)
        else:
            pattern_sim = 0.0

        # Context similarity
        context_sim = self._calculate_context_similarity(context, trace.context)

        # Recency factor
        time_diff = max(1, self.current_time - trace.last_accessed)
        recency_factor = 1.0 / math.log(1 + time_diff)

        # Frequency factor
        frequency_factor = math.log(1 + trace.access_count)

        # Combine factors
        score = (pattern_sim * (1.0 - self.context_weight) +
                context_sim * self.context_weight +
                recency_factor * self.recency_weight +
                frequency_factor * self.frequency_weight +
                trace.importance * self.importance_weight)

        return score

    def _calculate_context_similarity(self, context1: Dict[str, Any],
                                    context2: Dict[str, Any]) -> float:
        """Calculate similarity between contexts."""
        if not context1 or not context2:
            return 0.0

        # Jaccard similarity
        set1 = set(f"{k}:{v}" for k, v in context1.items())
        set2 = set(f"{k}:{v}" for k, v in context2.items())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def consolidate(self, max_consolidations: int = 10) -> int:
        """
        Perform memory consolidation.

        Args:
            max_consolidations: Maximum number of traces to consolidate

        Returns:
            Number of traces consolidated
        """
        consolidated_count = 0

        # Find traces ready for consolidation
        consolidation_candidates = []
        for trace_id, trace in self.memory_traces.items():
            if (trace.strength > self.consolidation_threshold and
                trace.consolidation_level == ConsolidationLevel.FRESH):
                consolidation_candidates.append((trace.strength, trace_id, trace))

        # Sort by strength and consolidate the strongest
        consolidation_candidates.sort(reverse=True)

        for _, trace_id, trace in consolidation_candidates[:max_consolidations]:
            self._consolidate_trace(trace)
            consolidated_count += 1
            self.consolidation_events += 1

        return consolidated_count

    def _consolidate_trace(self, trace: MemoryTrace):
        """Consolidate a single memory trace."""
        # Update consolidation level
        if trace.consolidation_level == ConsolidationLevel.FRESH:
            trace.consolidation_level = ConsolidationLevel.CONSOLIDATING
        elif trace.consolidation_level == ConsolidationLevel.CONSOLIDATING:
            trace.consolidation_level = ConsolidationLevel.CONSOLIDATED

        # Strengthen the trace
        trace.strength = min(1.0, trace.strength * 1.1)

        # Create stronger associations
        for assoc_id in trace.associations:
            if assoc_id in self.memory_traces:
                assoc_trace = self.memory_traces[assoc_id]
                assoc_trace.strength = min(1.0, assoc_trace.strength + 0.05)

    def forget_traces(self, max_forgetting: int = 5) -> int:
        """
        Apply active forgetting to weak or old traces.

        Args:
            max_forgetting: Maximum number of traces to forget

        Returns:
            Number of traces forgotten
        """
        forgotten_count = 0

        # Find candidates for forgetting
        forgetting_candidates = []
        for trace_id, trace in self.memory_traces.items():
            # Calculate forgetting score (higher = more likely to forget)
            age = self.current_time - trace.creation_time
            time_since_access = self.current_time - trace.last_accessed

            forgetting_score = (
                (1.0 - trace.strength) * 0.4 +
                (1.0 - trace.importance) * 0.3 +
                math.log(1 + time_since_access) * 0.2 +
                math.log(1 + age) * 0.1
            )

            # Don't forget highly consolidated memories
            if trace.consolidation_level != ConsolidationLevel.CRYSTALLIZED:
                forgetting_candidates.append((forgetting_score, trace_id))

        # Sort by forgetting score and forget the weakest
        forgetting_candidates.sort(reverse=True)

        for forgetting_score, trace_id in forgetting_candidates[:max_forgetting]:
            if forgetting_score > 1.0:  # Only forget if score is high enough
                self._forget_trace(trace_id)
                forgotten_count += 1
                self.forgetting_events += 1

        return forgotten_count

    def _forget_trace(self, trace_id: int):
        """Remove a trace from memory."""
        if trace_id not in self.memory_traces:
            return

        trace = self.memory_traces[trace_id]

        # Remove from all indices
        pattern_hash = self._hash_pattern(trace.pattern)
        if pattern_hash in self.trace_index:
            del self.trace_index[pattern_hash]

        # Remove from context index
        for key, value in trace.context.items():
            context_key = f"{key}:{value}"
            if context_key in self.context_index:
                self.context_index[context_key].discard(trace_id)
                if not self.context_index[context_key]:
                    del self.context_index[context_key]

        # Remove from memory hierarchy
        if trace_id in self.memory_hierarchy[trace.memory_type]:
            del self.memory_hierarchy[trace.memory_type][trace_id]

        # Remove associations
        for assoc_id in trace.associations:
            if assoc_id in self.memory_traces:
                self.memory_traces[assoc_id].associations.discard(trace_id)
            if assoc_id in self.association_graph:
                self.association_graph[assoc_id].discard(trace_id)

        if trace_id in self.association_graph:
            del self.association_graph[trace_id]

        # Remove the trace itself
        del self.memory_traces[trace_id]

    def _hash_pattern(self, pattern: np.ndarray) -> str:
        """Create a hash for pattern matching."""
        # Use sparse representation for hashing
        active_indices = np.where(pattern > 0.1)[0]
        return ",".join(map(str, active_indices))

    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics."""
        # Count traces by type
        type_counts = {}
        for memory_type in MemoryType:
            type_counts[memory_type.value] = len(self.memory_hierarchy[memory_type])

        # Count by consolidation level
        consolidation_counts = {}
        for level in ConsolidationLevel:
            consolidation_counts[level.value] = sum(
                1 for trace in self.memory_traces.values()
                if trace.consolidation_level == level
            )

        # Calculate average statistics
        if self.memory_traces:
            avg_strength = np.mean([trace.strength for trace in self.memory_traces.values()])
            avg_importance = np.mean([trace.importance for trace in self.memory_traces.values()])
            avg_access_count = np.mean([trace.access_count for trace in self.memory_traces.values()])
        else:
            avg_strength = avg_importance = avg_access_count = 0.0

        return {
            "ams_id": self.id,
            "total_traces": len(self.memory_traces),
            "max_capacity": self.max_traces,
            "memory_usage": len(self.memory_traces) / self.max_traces,
            "type_distribution": type_counts,
            "consolidation_distribution": consolidation_counts,
            "average_strength": avg_strength,
            "average_importance": avg_importance,
            "average_access_count": avg_access_count,
            "total_stores": self.total_stores,
            "total_retrievals": self.total_retrievals,
            "consolidation_events": self.consolidation_events,
            "forgetting_events": self.forgetting_events,
            "association_count": len(self.association_graph)
        }

    def _manage_memory_capacity(self):
        """Manage memory when approaching capacity limits."""
        if len(self.memory_traces) <= self.max_traces:
            return

        # Calculate how many traces to remove
        excess = len(self.memory_traces) - self.max_traces
        target_removals = excess + int(self.max_traces * 0.1)  # Remove 10% extra

        # Apply forgetting to make space
        forgotten = self.forget_traces(target_removals)

        # If still over capacity, remove oldest weak traces
        if len(self.memory_traces) > self.max_traces:
            remaining_excess = len(self.memory_traces) - self.max_traces
            self._emergency_cleanup(remaining_excess)

    def _emergency_cleanup(self, num_to_remove: int):
        """Emergency cleanup when memory is critically full."""
        # Find weakest traces for removal
        weak_traces = []
        for trace_id, trace in self.memory_traces.items():
            if trace.consolidation_level != ConsolidationLevel.CRYSTALLIZED:
                weakness_score = (
                    (1.0 - trace.strength) * 0.5 +
                    (1.0 - trace.importance) * 0.3 +
                    (self.current_time - trace.last_accessed) * 0.2
                )
                weak_traces.append((weakness_score, trace_id))

        # Sort by weakness and remove the weakest
        weak_traces.sort(reverse=True)
        for _, trace_id in weak_traces[:num_to_remove]:
            self._forget_trace(trace_id)

    def save_state(self) -> Dict[str, Any]:
        """Save complete AMS state."""
        # Convert traces to serializable format
        traces_data = {}
        for trace_id, trace in self.memory_traces.items():
            traces_data[trace_id] = {
                "pattern": trace.pattern.tolist(),
                "memory_type": trace.memory_type.value,
                "consolidation_level": trace.consolidation_level.value,
                "strength": trace.strength,
                "creation_time": trace.creation_time,
                "last_accessed": trace.last_accessed,
                "access_count": trace.access_count,
                "context": trace.context,
                "associations": list(trace.associations),
                "importance": trace.importance,
                "surprise": trace.surprise,
                "emotional_valence": trace.emotional_valence
            }

        return {
            "ams_id": self.id,
            "representation_size": self.representation_size,
            "max_traces": self.max_traces,
            "current_time": self.current_time,
            "next_trace_id": self.next_trace_id,
            "traces": traces_data,
            "trace_index": self.trace_index,
            "context_index": {k: list(v) for k, v in self.context_index.items()},
            "association_graph": {k: list(v) for k, v in self.association_graph.items()},
            "statistics": {
                "total_stores": self.total_stores,
                "total_retrievals": self.total_retrievals,
                "consolidation_events": self.consolidation_events,
                "forgetting_events": self.forgetting_events
            }
        }

    def reset_state(self):
        """Reset AMS to initial state."""
        self.memory_traces.clear()
        self.trace_index.clear()
        self.context_index.clear()
        self.association_graph.clear()

        for memory_type in MemoryType:
            self.memory_hierarchy[memory_type].clear()

        self.current_time = 0
        self.next_trace_id = 0
        self.consolidation_queue.clear()
        self.forgetting_queue.clear()

        self.total_stores = 0
        self.total_retrievals = 0
        self.consolidation_events = 0
        self.forgetting_events = 0

    def __repr__(self):
        stats = self.get_memory_statistics()
        return (f"AdvancedAMS(id={self.id}, "
                f"traces={stats['total_traces']}/{stats['max_capacity']}, "
                f"usage={stats['memory_usage']:.2f}, "
                f"avg_strength={stats['average_strength']:.3f})")


# Backward compatibility class
class AMS(AdvancedAMS):
    """Backward compatible AMS class."""

    def __init__(self, ams_id, representation_size):
        super().__init__(ams_id, representation_size, max_traces=1000)
        self.memory_traces_list = []  # For backward compatibility

    def store_pattern(self, pattern, metadata=None):
        """Store pattern with backward compatibility."""
        if isinstance(pattern, list):
            pattern = np.array(pattern)

        context = metadata or {}
        trace_id = super().store_pattern(
            pattern=pattern,
            memory_type=MemoryType.EPISODIC,
            context=context,
            importance=0.5
        )

        # Maintain backward compatible list
        trace = self.memory_traces[trace_id]
        legacy_trace = {
            "pattern": trace.pattern,
            "metadata": trace.context
        }
        self.memory_traces_list.append(legacy_trace)

        return trace_id

    def retrieve_similar(self, cue_pattern, top_k=1, similarity_threshold=0.5):
        """Retrieve with backward compatibility."""
        if isinstance(cue_pattern, list):
            cue_pattern = np.array(cue_pattern)

        results = super().retrieve_similar(
            cue_pattern=cue_pattern,
            top_k=top_k,
            similarity_threshold=similarity_threshold
        )

        # Convert to legacy format
        legacy_results = []
        for score, trace in results:
            legacy_trace = {
                "pattern": trace.pattern,
                "metadata": trace.context
            }
            legacy_results.append((score, legacy_trace))

        return legacy_results

    def consolidate(self):
        """Consolidate with backward compatibility."""
        return super().consolidate(max_consolidations=5)

    def _update_existing_trace(self, trace_id: int, context: Dict[str, Any],
                              importance: float):
        """Update an existing memory trace."""
        trace = self.memory_traces[trace_id]

        # Update access information
        trace.last_accessed = self.current_time
        trace.access_count += 1

        # Update strength based on repeated exposure
        trace.strength = min(1.0, trace.strength + 0.1)

        # Update importance
        trace.importance = max(trace.importance, importance)

        # Merge context information
        for key, value in context.items():
            if key not in trace.context:
                trace.context[key] = value
                context_key = f"{key}:{value}"
                self.context_index[context_key].add(trace_id)



    def retrieve_similar(self, cue_pattern: np.ndarray, top_k: int = 5,
                        similarity_threshold: float = 0.3,
                        context: Dict[str, Any] = None,
                        memory_types: List[MemoryType] = None) -> List[Tuple[float, MemoryTrace]]:
        """
        Retrieve similar patterns with advanced scoring.

        Args:
            cue_pattern: Pattern to match
            top_k: Number of results to return
            similarity_threshold: Minimum similarity threshold
            context: Current context for context-dependent retrieval
            memory_types: Types of memory to search

        Returns:
            List of (score, trace) tuples sorted by relevance
        """
        if len(cue_pattern) != self.representation_size:
            raise ValueError(f"Cue pattern size mismatch")

        if context is None:
            context = {}

        if memory_types is None:
            memory_types = list(MemoryType)

        candidates = []

        # Collect candidate traces
        for memory_type in memory_types:
            for trace_id, trace in self.memory_hierarchy[memory_type].items():
                candidates.append((trace_id, trace))

        if not candidates:
            return []

        # Score each candidate
        scored_candidates = []
        for trace_id, trace in candidates:
            score = self._calculate_retrieval_score(
                cue_pattern, trace, context
            )

            if score >= similarity_threshold:
                scored_candidates.append((score, trace))

        # Sort by score and return top_k
        scored_candidates.sort(key=lambda x: x[0], reverse=True)

        # Update access information for retrieved traces
        for score, trace in scored_candidates[:top_k]:
            trace.last_accessed = self.current_time
            trace.access_count += 1

        self.total_retrievals += 1
        self.current_time += 1

        return scored_candidates[:top_k]

    def _calculate_retrieval_score(self, cue_pattern: np.ndarray,
                                  trace: MemoryTrace, context: Dict[str, Any]) -> float:
        """Calculate comprehensive retrieval score."""
        # Pattern similarity
        pattern_sim = self._calculate_similarity(cue_pattern, trace.pattern)

        # Context similarity
        context_sim = self._calculate_context_similarity(context, trace.context)

        # Recency factor (more recent = higher score)
        time_diff = max(1, self.current_time - trace.last_accessed)
        recency_factor = 1.0 / math.log(1 + time_diff)

        # Frequency factor (more accessed = higher score)
        frequency_factor = math.log(1 + trace.access_count)

        # Importance and strength factors
        importance_factor = trace.importance
        strength_factor = trace.strength

        # Emotional modulation
        emotional_factor = 1.0 + abs(trace.emotional_valence) * 0.2

        # Combine factors
        score = (pattern_sim * (1.0 - self.context_weight) +
                context_sim * self.context_weight +
                recency_factor * self.recency_weight +
                frequency_factor * self.frequency_weight +
                importance_factor * self.importance_weight) * emotional_factor

        return score

    def consolidate(self, max_consolidations: int = 10) -> int:
        """
        Perform memory consolidation.

        Args:
            max_consolidations: Maximum number of traces to consolidate

        Returns:
            Number of traces consolidated
        """
        consolidated_count = 0

        while (self.consolidation_queue and
               consolidated_count < max_consolidations):

            trace_id = self.consolidation_queue.popleft()
            if trace_id not in self.memory_traces:
                continue

            trace = self.memory_traces[trace_id]

            # Check if trace is ready for consolidation
            if (trace.strength > self.consolidation_threshold and
                trace.consolidation_level == ConsolidationLevel.FRESH):

                # Perform consolidation
                self._consolidate_trace(trace)
                consolidated_count += 1
                self.consolidation_events += 1

        return consolidated_count

    def _consolidate_trace(self, trace: MemoryTrace):
        """Consolidate a single memory trace."""
        # Update consolidation level
        if trace.consolidation_level == ConsolidationLevel.FRESH:
            trace.consolidation_level = ConsolidationLevel.CONSOLIDATING
        elif trace.consolidation_level == ConsolidationLevel.CONSOLIDATING:
            trace.consolidation_level = ConsolidationLevel.CONSOLIDATED
        elif trace.consolidation_level == ConsolidationLevel.CONSOLIDATED:
            trace.consolidation_level = ConsolidationLevel.CRYSTALLIZED

        # Strengthen the trace
        trace.strength = min(1.0, trace.strength * 1.1)

        # Compress pattern if highly consolidated
        if trace.consolidation_level == ConsolidationLevel.CRYSTALLIZED:
            trace.pattern = self._compress_pattern(trace.pattern)

        # Strengthen associations
        for assoc_id in trace.associations:
            if assoc_id in self.memory_traces:
                assoc_trace = self.memory_traces[assoc_id]
                assoc_trace.strength = min(1.0, assoc_trace.strength + 0.05)

    def _compress_pattern(self, pattern: np.ndarray) -> np.ndarray:
        """Compress a pattern while preserving important features."""
        # Simple compression: keep only the strongest activations
        threshold = np.percentile(pattern, 100 * (1 - self.compression_ratio))
        compressed = pattern.copy()
        compressed[compressed < threshold] = 0
        return compressed

