# -*- coding: utf-8 -*-

"""Enhanced Sparse Network implementation for the ASCE final model with advanced features."""

import random
import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Set, Any
from collections import defaultdict, deque
from .neuron import AdvancedNeuron, Neuron
from .connection import AdvancedConnection, Connection, SynapseType, NeurotransmitterType
from .plasticity import AdvancedPlasticityEngine

class NetworkTopology:
    """Manages network topology and connectivity patterns."""

    def __init__(self):
        self.layers = {}  # {layer_id: set of neuron_ids}
        self.modules = {}  # {module_id: set of neuron_ids}
        self.neuron_types = {}  # {neuron_id: neuron_type}
        self.connectivity_matrix = None

    def add_layer(self, layer_id: str, neuron_ids: Set[int]):
        """Add a layer of neurons."""
        self.layers[layer_id] = set(neuron_ids)

    def add_module(self, module_id: str, neuron_ids: Set[int]):
        """Add a functional module."""
        self.modules[module_id] = set(neuron_ids)

    def get_layer_neurons(self, layer_id: str) -> Set[int]:
        """Get neurons in a specific layer."""
        return self.layers.get(layer_id, set())

class AdvancedSparseNetwork:
    """
    Advanced sparse network with sophisticated dynamics and organization.

    Features:
    - Hierarchical organization (layers, modules)
    - Multiple neuron and synapse types
    - Advanced plasticity mechanisms
    - Structural plasticity and pruning
    - Neuromodulation
    - Activity-dependent homeostasis
    - Predictive coding capabilities
    """

    def __init__(self, enable_structural_plasticity: bool = True,
                 enable_homeostasis: bool = True,
                 max_neurons: int = 10000):
        """
        Initialize the advanced sparse network.

        Args:
            enable_structural_plasticity: Enable dynamic connection formation/pruning
            enable_homeostasis: Enable homeostatic mechanisms
            max_neurons: Maximum number of neurons allowed
        """
        # Core network structure
        self.neurons = {}  # {neuron_id: AdvancedNeuron}
        self.outgoing_connections = defaultdict(dict)  # {pre_id: {post_id: AdvancedConnection}}
        self.incoming_connections = defaultdict(dict)  # {post_id: {pre_id: AdvancedConnection}}

        # Network organization
        self.topology = NetworkTopology()
        self._next_neuron_id = 0
        self.max_neurons = max_neurons

        # Advanced features
        self.enable_structural_plasticity = enable_structural_plasticity
        self.enable_homeostasis = enable_homeostasis
        self.plasticity_engine = AdvancedPlasticityEngine()

        # Network state
        self.current_time = 0
        self.global_activity_level = 0.0
        self.network_energy = 0.0

        # Neuromodulation
        self.neuromodulator_levels = {
            "dopamine": 0.5,
            "acetylcholine": 0.5,
            "norepinephrine": 0.5,
            "serotonin": 0.5
        }

        # Activity monitoring
        self.activity_history = deque(maxlen=1000)
        self.firing_patterns = {}
        self.pattern_frequencies = defaultdict(int)

        # Homeostatic parameters
        self.target_sparsity = 0.05  # Target fraction of active neurons
        self.homeostatic_time_constant = 100

        # Structural plasticity parameters
        self.connection_formation_rate = 0.001
        self.connection_pruning_threshold = 0.1
        self.max_connections_per_neuron = 100

        # Performance metrics
        self.total_spikes = 0
        self.total_computations = 0
        self.energy_efficiency = 0.0

    def add_neuron(self, neuron_type: str = "excitatory", layer_id: str = None,
                   module_id: str = None, **kwargs) -> int:
        """
        Add an advanced neuron to the network.

        Args:
            neuron_type: Type of neuron ('excitatory', 'inhibitory', 'modulatory')
            layer_id: Layer identifier for organization
            module_id: Module identifier for organization
            **kwargs: Additional neuron parameters

        Returns:
            Neuron ID
        """
        if len(self.neurons) >= self.max_neurons:
            raise ValueError(f"Maximum number of neurons ({self.max_neurons}) reached")

        neuron_id = self._next_neuron_id

        # Create advanced neuron
        new_neuron = AdvancedNeuron(
            neuron_id=neuron_id,
            neuron_type=neuron_type,
            **kwargs
        )

        self.neurons[neuron_id] = new_neuron
        self.outgoing_connections[neuron_id] = {}
        self.incoming_connections[neuron_id] = {}

        # Add to topology
        self.topology.neuron_types[neuron_id] = neuron_type
        if layer_id:
            if layer_id not in self.topology.layers:
                self.topology.layers[layer_id] = set()
            self.topology.layers[layer_id].add(neuron_id)

        if module_id:
            if module_id not in self.topology.modules:
                self.topology.modules[module_id] = set()
            self.topology.modules[module_id].add(neuron_id)

        self._next_neuron_id += 1
        return neuron_id

    def add_connection(self, pre_neuron_id: int, post_neuron_id: int,
                      weight: float = None, synapse_type: SynapseType = SynapseType.EXCITATORY,
                      neurotransmitter: NeurotransmitterType = NeurotransmitterType.GLUTAMATE,
                      **kwargs) -> Optional[AdvancedConnection]:
        """
        Add an advanced connection between neurons.

        Args:
            pre_neuron_id: Pre-synaptic neuron ID
            post_neuron_id: Post-synaptic neuron ID
            weight: Connection weight
            synapse_type: Type of synapse
            neurotransmitter: Neurotransmitter type
            **kwargs: Additional connection parameters

        Returns:
            AdvancedConnection object or None if connection exists
        """
        # Validate neurons exist
        if pre_neuron_id not in self.neurons or post_neuron_id not in self.neurons:
            raise ValueError("Both pre- and post-synaptic neurons must exist")

        # Check if connection already exists
        if post_neuron_id in self.outgoing_connections[pre_neuron_id]:
            return None

        # Check connection limits
        if len(self.outgoing_connections[pre_neuron_id]) >= self.max_connections_per_neuron:
            return None

        # Set default weight based on synapse type
        if weight is None:
            if synapse_type == SynapseType.EXCITATORY:
                weight = random.uniform(0.3, 0.8)
            elif synapse_type == SynapseType.INHIBITORY:
                weight = random.uniform(0.2, 0.6)
            else:
                weight = random.uniform(0.1, 0.5)

        # Create advanced connection
        new_connection = AdvancedConnection(
            pre_neuron_id=pre_neuron_id,
            post_neuron_id=post_neuron_id,
            weight=weight,
            synapse_type=synapse_type,
            neurotransmitter=neurotransmitter,
            **kwargs
        )

        # Add to network
        self.outgoing_connections[pre_neuron_id][post_neuron_id] = new_connection
        self.incoming_connections[post_neuron_id][pre_neuron_id] = new_connection

        return new_connection

    def get_neuron(self, neuron_id: int) -> Optional[AdvancedNeuron]:
        """Get neuron by ID."""
        return self.neurons.get(neuron_id)

    def get_connection(self, pre_neuron_id: int, post_neuron_id: int) -> Optional[AdvancedConnection]:
        """Get connection between two neurons."""
        return self.outgoing_connections.get(pre_neuron_id, {}).get(post_neuron_id)

    def stimulate_neuron(self, neuron_id: int, input_value: float,
                        source_type: str = "external",
                        neurotransmitter: str = "glutamate"):
        """Apply external stimulation to a neuron."""
        neuron = self.get_neuron(neuron_id)
        if neuron:
            neuron.integrate_input(input_value, source_type, neurotransmitter)
        else:
            print(f"Warning: Neuron {neuron_id} not found for stimulation")

    def step(self, apply_plasticity: bool = True,
             modulatory_signals: Dict[str, float] = None,
             context_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform one simulation step with advanced dynamics.

        Args:
            apply_plasticity: Whether to apply plasticity rules
            modulatory_signals: Neuromodulator concentrations
            context_info: Contextual information for learning

        Returns:
            Dictionary with step statistics
        """
        if modulatory_signals is None:
            modulatory_signals = {}
        if context_info is None:
            context_info = {}

        self.current_time += 1
        step_stats = {
            "fired_neurons": set(),
            "total_spikes": 0,
            "network_activity": 0.0,
            "energy_consumed": 0.0,
            "plasticity_events": 0
        }

        # 1. Update neuromodulator levels
        self._update_neuromodulation(modulatory_signals)

        # 2. Propagate signals from previously fired neurons
        fired_last_tick = {nid for nid, n in self.neurons.items() if n.fired_this_tick}

        for pre_neuron_id in fired_last_tick:
            pre_neuron = self.neurons[pre_neuron_id]

            # Propagate through outgoing connections
            for post_neuron_id, connection in self.outgoing_connections[pre_neuron_id].items():
                post_neuron = self.neurons[post_neuron_id]

                # Transmit signal through advanced connection
                if hasattr(connection, 'transmit'):
                    signal = connection.transmit(
                        pre_spike_time=self.current_time - 1,
                        post_spike_time=self.current_time,
                        current_time=self.current_time,
                        neuromodulation=self.neuromodulator_levels
                    )
                else:
                    # Fallback for simple connections
                    signal = connection.weight

                # Apply signal to post-synaptic neuron
                post_neuron.integrate_input(signal)

                # Track energy consumption
                step_stats["energy_consumed"] += abs(signal) * 0.001

        # 3. Update all neurons
        for neuron in self.neurons.values():
            neuron.update_potential(
                neuromodulation=self.neuromodulator_levels
            )

            if neuron.fired_this_tick:
                step_stats["fired_neurons"].add(neuron.id)
                step_stats["total_spikes"] += 1
                self.total_spikes += 1

        # 4. Calculate network activity
        step_stats["network_activity"] = len(step_stats["fired_neurons"]) / len(self.neurons)
        self.global_activity_level = step_stats["network_activity"]
        self.activity_history.append(step_stats["network_activity"])

        # 5. Apply plasticity mechanisms
        if apply_plasticity:
            plasticity_stats = self.plasticity_engine.apply_advanced_edsp(
                network=self,
                modulatory_signals=modulatory_signals,
                context_info=context_info
            )
            step_stats["plasticity_events"] = plasticity_stats.get("plasticity_events", 0)

        # 6. Apply homeostatic mechanisms
        if self.enable_homeostasis:
            self._apply_homeostasis()

        # 7. Apply structural plasticity
        if self.enable_structural_plasticity:
            structural_stats = self._apply_structural_plasticity()
            step_stats.update(structural_stats)

        # 8. Update performance metrics
        self.total_computations += 1
        self.network_energy += step_stats["energy_consumed"]
        self._update_efficiency_metrics()

        # 9. Record firing patterns
        self._record_firing_pattern(step_stats["fired_neurons"])

        return step_stats

    def _update_neuromodulation(self, modulatory_signals: Dict[str, float]):
        """Update neuromodulator levels based on signals."""
        decay_rate = 0.95

        # Decay existing levels
        for nt in self.neuromodulator_levels:
            self.neuromodulator_levels[nt] *= decay_rate

        # Apply new signals
        for nt, level in modulatory_signals.items():
            if nt in self.neuromodulator_levels:
                self.neuromodulator_levels[nt] = max(0.0, min(1.0,
                    self.neuromodulator_levels[nt] + level * 0.1))

    def _apply_homeostasis(self):
        """Apply homeostatic mechanisms to maintain network stability."""
        if len(self.activity_history) < 10:
            return

        # Calculate recent average activity
        recent_activity = sum(list(self.activity_history)[-10:]) / 10

        # Adjust neuron excitability based on activity
        activity_error = self.target_sparsity - recent_activity

        for neuron in self.neurons.values():
            if hasattr(neuron, 'intrinsic_excitability'):
                adjustment = activity_error * 0.001
                neuron.intrinsic_excitability += adjustment
                neuron.intrinsic_excitability = max(0.1, min(2.0, neuron.intrinsic_excitability))

    def _apply_structural_plasticity(self) -> Dict[str, int]:
        """Apply structural plasticity (connection formation and pruning)."""
        formed_connections = 0
        pruned_connections = 0

        # Prune weak connections
        connections_to_prune = []
        for pre_id, connections in self.outgoing_connections.items():
            for post_id, connection in connections.items():
                if hasattr(connection, 'should_be_pruned') and connection.should_be_pruned():
                    connections_to_prune.append((pre_id, post_id))

        for pre_id, post_id in connections_to_prune:
            self._remove_connection(pre_id, post_id)
            pruned_connections += 1

        # Form new connections based on activity correlation
        if random.random() < self.connection_formation_rate:
            new_connection = self._form_activity_dependent_connection()
            if new_connection:
                formed_connections += 1

        return {
            "formed_connections": formed_connections,
            "pruned_connections": pruned_connections
        }

    def _remove_connection(self, pre_id: int, post_id: int):
        """Remove a connection from the network."""
        if pre_id in self.outgoing_connections and post_id in self.outgoing_connections[pre_id]:
            del self.outgoing_connections[pre_id][post_id]

        if post_id in self.incoming_connections and pre_id in self.incoming_connections[post_id]:
            del self.incoming_connections[post_id][pre_id]

    def _form_activity_dependent_connection(self) -> Optional[AdvancedConnection]:
        """Form new connection based on activity correlation."""
        # Find neurons with correlated activity
        active_neurons = [nid for nid, n in self.neurons.items() if n.fired_this_tick]

        if len(active_neurons) < 2:
            return None

        # Randomly select two active neurons
        pre_id, post_id = random.sample(active_neurons, 2)

        # Check if connection already exists
        if post_id in self.outgoing_connections[pre_id]:
            return None

        # Create new connection with low initial weight
        return self.add_connection(
            pre_neuron_id=pre_id,
            post_neuron_id=post_id,
            weight=0.1,
            synapse_type=SynapseType.PLASTIC
        )

    def _update_efficiency_metrics(self):
        """Update network efficiency metrics."""
        if self.total_computations > 0:
            self.energy_efficiency = self.total_spikes / max(self.network_energy, 0.001)

    def _record_firing_pattern(self, fired_neurons: Set[int]):
        """Record and analyze firing patterns."""
        pattern = tuple(sorted(fired_neurons))
        self.pattern_frequencies[pattern] += 1

        # Keep only recent patterns
        if len(self.pattern_frequencies) > 1000:
            # Remove least frequent patterns
            sorted_patterns = sorted(self.pattern_frequencies.items(), key=lambda x: x[1])
            for pattern, _ in sorted_patterns[:100]:
                del self.pattern_frequencies[pattern]

    def get_network_statistics(self) -> Dict[str, Any]:
        """Get comprehensive network statistics."""
        total_connections = sum(len(conns) for conns in self.outgoing_connections.values())
        avg_connections_per_neuron = total_connections / max(len(self.neurons), 1)

        # Calculate sparsity
        max_possible_connections = len(self.neurons) * (len(self.neurons) - 1)
        sparsity = 1.0 - (total_connections / max(max_possible_connections, 1))

        # Calculate activity statistics
        recent_activity = list(self.activity_history)[-100:] if self.activity_history else [0]
        avg_activity = sum(recent_activity) / len(recent_activity)

        return {
            "total_neurons": len(self.neurons),
            "total_connections": total_connections,
            "avg_connections_per_neuron": avg_connections_per_neuron,
            "network_sparsity": sparsity,
            "current_activity": self.global_activity_level,
            "average_activity": avg_activity,
            "total_spikes": self.total_spikes,
            "total_computations": self.total_computations,
            "energy_efficiency": self.energy_efficiency,
            "unique_patterns": len(self.pattern_frequencies),
            "neuromodulator_levels": self.neuromodulator_levels.copy()
        }

    def save_state(self) -> Dict[str, Any]:
        """Save complete network state for serialization."""
        neuron_states = {}
        for nid, neuron in self.neurons.items():
            if hasattr(neuron, 'get_state_dict'):
                neuron_states[nid] = neuron.get_state_dict()

        connection_states = {}
        for pre_id, connections in self.outgoing_connections.items():
            connection_states[pre_id] = {}
            for post_id, connection in connections.items():
                if hasattr(connection, 'get_state_dict'):
                    connection_states[pre_id][post_id] = connection.get_state_dict()

        return {
            "neurons": neuron_states,
            "connections": connection_states,
            "topology": {
                "layers": {k: list(v) for k, v in self.topology.layers.items()},
                "modules": {k: list(v) for k, v in self.topology.modules.items()},
                "neuron_types": self.topology.neuron_types.copy()
            },
            "network_state": {
                "current_time": self.current_time,
                "neuromodulator_levels": self.neuromodulator_levels.copy(),
                "global_activity_level": self.global_activity_level,
                "total_spikes": self.total_spikes,
                "total_computations": self.total_computations
            },
            "plasticity_state": self.plasticity_engine.get_learning_statistics()
        }

    def create_layer_connections(self, source_layer: str, target_layer: str,
                                connection_probability: float = 0.1,
                                weight_range: Tuple[float, float] = (0.1, 0.5)):
        """Create connections between layers with specified probability."""
        source_neurons = self.topology.get_layer_neurons(source_layer)
        target_neurons = self.topology.get_layer_neurons(target_layer)

        connections_created = 0
        for pre_id in source_neurons:
            for post_id in target_neurons:
                if random.random() < connection_probability:
                    weight = random.uniform(*weight_range)
                    connection = self.add_connection(pre_id, post_id, weight=weight)
                    if connection:
                        connections_created += 1

        return connections_created

    def apply_global_inhibition(self, inhibition_strength: float = 0.1):
        """Apply global inhibitory signal to all neurons."""
        for neuron in self.neurons.values():
            neuron.integrate_input(-inhibition_strength, "global_inhibition", "gaba")

    def reset_network_state(self):
        """Reset network to initial state while preserving structure."""
        for neuron in self.neurons.values():
            neuron.potential = 0.0
            neuron.fired_this_tick = False
            neuron.refractory_period = 0
            if hasattr(neuron, 'firing_history'):
                neuron.firing_history.clear()

        self.current_time = 0
        self.global_activity_level = 0.0
        self.activity_history.clear()
        self.pattern_frequencies.clear()

    def __repr__(self):
        stats = self.get_network_statistics()
        return (f"AdvancedSparseNetwork(neurons={stats['total_neurons']}, "
                f"connections={stats['total_connections']}, "
                f"sparsity={stats['network_sparsity']:.3f}, "
                f"activity={stats['current_activity']:.3f})")


# Backward compatibility class
class SparseNetwork(AdvancedSparseNetwork):
    """Backward compatible sparse network class."""

    def __init__(self):
        super().__init__(enable_structural_plasticity=False, enable_homeostasis=False)
        self._next_neuron_id = 0

    def add_neuron(self, threshold=0.5, potential=0.0):
        """Add a simple neuron for backward compatibility."""
        from .neuron import Neuron

        neuron_id = self._next_neuron_id
        new_neuron = Neuron(neuron_id, threshold=threshold, potential=potential)
        self.neurons[neuron_id] = new_neuron
        self.outgoing_connections[neuron_id] = {}
        self.incoming_connections[neuron_id] = {}
        self._next_neuron_id += 1
        return neuron_id

    def add_connection(self, pre_neuron_id, post_neuron_id, weight=None, delay=1):
        """Add a simple connection for backward compatibility."""
        from .connection import Connection

        if pre_neuron_id not in self.neurons or post_neuron_id not in self.neurons:
            raise ValueError("Both pre- and post-synaptic neurons must exist.")
        if post_neuron_id in self.outgoing_connections.get(pre_neuron_id, {}):
            return None

        initial_weight = weight if weight is not None else random.uniform(0.1, 0.9)
        new_connection = Connection(pre_neuron_id, post_neuron_id, weight=initial_weight, delay=delay)
        self.outgoing_connections[pre_neuron_id][post_neuron_id] = new_connection
        self.incoming_connections[post_neuron_id][pre_neuron_id] = new_connection
        return new_connection

    def step(self, apply_plasticity_func=None, modulatory_signal=1.0):
        """Simple step function for backward compatibility."""
        # Get fired neurons from last tick
        fired_last_tick_ids = {nid for nid, n in self.neurons.items() if n.fired_this_tick}

        # Propagate signals
        for pre_neuron_id in fired_last_tick_ids:
            for post_neuron_id, connection in self.outgoing_connections.get(pre_neuron_id, {}).items():
                post_neuron = self.get_neuron(post_neuron_id)
                if post_neuron:
                    post_neuron.integrate_input(connection.weight)

        # Update neurons
        for neuron in self.neurons.values():
            neuron.update_potential()

        # Apply plasticity
        if apply_plasticity_func:
            apply_plasticity_func(self, modulatory_signal)

        # Return fired neurons
        fired_this_tick_ids = {nid for nid, n in self.neurons.items() if n.fired_this_tick}
        return fired_this_tick_ids

