# -*- coding: utf-8 -*-

"""Implements the Sparse Network structure for the ASCE prototype."""

import random
from .neuron import Neuron
from .connection import Connection
# Import plasticity rules later when defined
# from .plasticity import apply_edsp

class SparseNetwork:
    """Manages a collection of neurons and their sparse connections."""
    def __init__(self):
        """Initializes an empty sparse network."""
        self.neurons = {} # {neuron_id: Neuron object}
        # Using dictionaries for sparse connections:
        self.outgoing_connections = {} # {neuron_id: {connected_neuron_id: Connection object}}
        self.incoming_connections = {} # {neuron_id: {connected_neuron_id: Connection object}}
        self._next_neuron_id = 0

    def add_neuron(self, threshold=0.5, potential=0.0):
        """Adds a new neuron to the network."""
        neuron_id = self._next_neuron_id
        new_neuron = Neuron(neuron_id, threshold=threshold, potential=potential)
        self.neurons[neuron_id] = new_neuron
        self.outgoing_connections[neuron_id] = {}
        self.incoming_connections[neuron_id] = {}
        self._next_neuron_id += 1
        return neuron_id

    def add_connection(self, pre_neuron_id, post_neuron_id, weight=None, delay=1):
        """Adds a directed connection between two existing neurons."""
        if pre_neuron_id not in self.neurons or post_neuron_id not in self.neurons:
            raise ValueError("Both pre- and post-synaptic neurons must exist.")
        if post_neuron_id in self.outgoing_connections.get(pre_neuron_id, {}):
            # print(f"Warning: Connection {pre_neuron_id} -> {post_neuron_id} already exists. Skipping.")
            return # Avoid duplicate connections

        # Initialize with random weight if not specified
        initial_weight = weight if weight is not None else random.uniform(0.1, 0.9)

        new_connection = Connection(pre_neuron_id, post_neuron_id, weight=initial_weight, delay=delay)
        self.outgoing_connections[pre_neuron_id][post_neuron_id] = new_connection
        self.incoming_connections[post_neuron_id][pre_neuron_id] = new_connection
        return new_connection

    def get_neuron(self, neuron_id):
        return self.neurons.get(neuron_id)

    def get_connection(self, pre_neuron_id, post_neuron_id):
        return self.outgoing_connections.get(pre_neuron_id, {}).get(post_neuron_id)

    def stimulate_neuron(self, neuron_id, input_value):
        """Applies an external input directly to a neuron for the current tick."""
        neuron = self.get_neuron(neuron_id)
        if neuron:
            neuron.integrate_input(input_value)
        else:
            print(f"Warning: Neuron {neuron_id} not found for stimulation.")

    def step(self, apply_plasticity_func=None, modulatory_signal=1.0):
        """Simulates one time step (tick) of the network.

        Args:
            apply_plasticity_func: A function to apply plasticity rules.
                                     Expected signature: func(network, mod_signal)
            modulatory_signal: A global signal affecting plasticity (e.g., learning rate).
        """
        # 1. Propagate signals from neurons that fired *last* tick
        fired_last_tick_ids = {nid for nid, n in self.neurons.items() if n.fired_this_tick}

        for pre_neuron_id in fired_last_tick_ids:
            # Find outgoing connections for the neuron that just fired
            for post_neuron_id, connection in self.outgoing_connections.get(pre_neuron_id, {}).items():
                post_neuron = self.get_neuron(post_neuron_id)
                if post_neuron:
                    # Integrate weighted input (delay ignored for simplicity)
                    post_neuron.integrate_input(connection.weight)

        # 2. Update potential for all neurons (includes decay and firing check)
        for neuron in self.neurons.values():
            neuron.update_potential()

        # 3. Apply plasticity rules (event-driven)
        if apply_plasticity_func:
            apply_plasticity_func(self, modulatory_signal)

        # Collect IDs of neurons that fired in *this* tick for the next step
        fired_this_tick_ids = {nid for nid, n in self.neurons.items() if n.fired_this_tick}
        return fired_this_tick_ids

    def __repr__(self):
        return f"SparseNetwork(neurons={len(self.neurons)}, connections={sum(len(conns) for conns in self.outgoing_connections.values())})"

