# -*- coding: utf-8 -*-

"""Defines the Connection between Neurons for the ASCE prototype."""

class Connection:
    """Represents a directed connection between two neurons."""
    def __init__(self, pre_neuron_id, post_neuron_id, weight=0.5, delay=1):
        """Initializes a Connection.

        Args:
            pre_neuron_id: The ID of the pre-synaptic neuron.
            post_neuron_id: The ID of the post-synaptic neuron.
            weight: The strength of the connection.
            delay: The transmission delay in simulation ticks (simplified to 1 for now).
        """
        self.pre_neuron_id = pre_neuron_id
        self.post_neuron_id = post_neuron_id
        self.weight = weight
        self.delay = delay # Currently unused in simple tick-based simulation

    def update_weight(self, delta_weight):
        """Applies a change to the connection weight."""
        self.weight += delta_weight
        # Optional: Add weight constraints (e.g., clamping between 0 and 1)
        self.weight = max(0.0, min(1.0, self.weight))

    def __repr__(self):
        return f"Connection({self.pre_neuron_id} -> {self.post_neuron_id}, w={self.weight:.2f})"

