# -*- coding: utf-8 -*-

"""Enhanced Connection implementation for the ASCE final model with advanced synaptic dynamics."""

import numpy as np
import math
from typing import Dict, List, Optional, Tuple
from enum import Enum

class SynapseType(Enum):
    """Types of synaptic connections."""
    EXCITATORY = "excitatory"
    INHIBITORY = "inhibitory"
    MODULATORY = "modulatory"
    PLASTIC = "plastic"
    STRUCTURAL = "structural"

class NeurotransmitterType(Enum):
    """Types of neurotransmitters."""
    GLUTAMATE = "glutamate"
    GABA = "gaba"
    DOPAMINE = "dopamine"
    ACETYLCHOLINE = "acetylcholine"
    NOREPINEPHRINE = "norepinephrine"
    SEROTONIN = "serotonin"

class AdvancedConnection:
    """
    Advanced synaptic connection with sophisticated plasticity and dynamics.

    Features:
    - Multiple plasticity mechanisms (STDP, homeostatic, metaplasticity)
    - Neurotransmitter-specific dynamics
    - Structural plasticity
    - Synaptic tagging and capture
    - Short-term and long-term plasticity
    """

    def __init__(self, pre_neuron_id: int, post_neuron_id: int,
                 weight: float = 0.5, synapse_type: SynapseType = SynapseType.EXCITATORY,
                 neurotransmitter: NeurotransmitterType = NeurotransmitterType.GLUTAMATE,
                 delay: int = 1, max_weight: float = 2.0, min_weight: float = 0.0):
        """
        Initialize an advanced synaptic connection.

        Args:
            pre_neuron_id: Pre-synaptic neuron ID
            post_neuron_id: Post-synaptic neuron ID
            weight: Initial synaptic weight
            synapse_type: Type of synapse
            neurotransmitter: Primary neurotransmitter
            delay: Transmission delay in time steps
            max_weight: Maximum allowed weight
            min_weight: Minimum allowed weight
        """
        self.pre_neuron_id = pre_neuron_id
        self.post_neuron_id = post_neuron_id
        self.weight = weight
        self.initial_weight = weight
        self.synapse_type = synapse_type
        self.neurotransmitter = neurotransmitter
        self.delay = delay
        self.max_weight = max_weight
        self.min_weight = min_weight

        # Plasticity parameters
        self.learning_rate = 0.01
        self.stdp_tau_plus = 20.0  # STDP time constant for potentiation
        self.stdp_tau_minus = 20.0  # STDP time constant for depression
        self.stdp_a_plus = 0.01    # STDP amplitude for potentiation
        self.stdp_a_minus = 0.01   # STDP amplitude for depression

        # Metaplasticity (plasticity of plasticity)
        self.metaplasticity_threshold = 1.0
        self.metaplasticity_factor = 1.0
        self.activity_history = []

        # Short-term plasticity
        self.facilitation_factor = 1.0
        self.depression_factor = 1.0
        self.facilitation_tau = 100.0
        self.depression_tau = 500.0
        self.use_probability = 0.5

        # Synaptic tagging and capture
        self.tag_strength = 0.0
        self.tag_decay = 0.95
        self.capture_threshold = 0.5

        # Structural plasticity
        self.structural_strength = 1.0
        self.pruning_threshold = 0.1
        self.growth_factor = 0.001

        # Usage and maintenance
        self.usage_trace = 0.0
        self.usage_decay = 0.999
        self.last_active_time = 0
        self.total_transmissions = 0

        # Homeostatic scaling
        self.homeostatic_scaling = 1.0
        self.target_activity = 0.1

        # Delay buffer for transmission delays
        self.delay_buffer = [0.0] * max(1, delay)
        self.buffer_index = 0

    def transmit(self, pre_spike_time: float, post_spike_time: float,
                current_time: int, neuromodulation: Dict[str, float] = None) -> float:
        """
        Transmit signal through synapse with advanced dynamics.

        Args:
            pre_spike_time: Time of pre-synaptic spike
            post_spike_time: Time of post-synaptic spike
            current_time: Current simulation time
            neuromodulation: Neuromodulator concentrations

        Returns:
            Transmitted signal strength
        """
        if neuromodulation is None:
            neuromodulation = {}

        # Update usage trace
        self.usage_trace = self.usage_trace * self.usage_decay + 1.0
        self.last_active_time = current_time
        self.total_transmissions += 1

        # Calculate short-term plasticity effects
        effective_weight = self._apply_short_term_plasticity()

        # Apply neuromodulation
        modulated_weight = self._apply_neuromodulation(effective_weight, neuromodulation)

        # Add to delay buffer
        signal = modulated_weight * self.structural_strength
        self.delay_buffer[self.buffer_index] = signal
        self.buffer_index = (self.buffer_index + 1) % len(self.delay_buffer)

        # Return delayed signal
        delayed_signal = self.delay_buffer[self.buffer_index]

        # Update synaptic tag based on activity
        self._update_synaptic_tag(delayed_signal)

        return delayed_signal

    def _apply_short_term_plasticity(self) -> float:
        """Apply short-term facilitation and depression."""
        # Update facilitation and depression factors
        self.facilitation_factor *= math.exp(-1.0 / self.facilitation_tau)
        self.depression_factor *= math.exp(-1.0 / self.depression_tau)

        # Apply facilitation on spike
        self.facilitation_factor += (1.0 - self.facilitation_factor) * 0.1

        # Apply depression based on usage
        effective_use = self.use_probability * self.facilitation_factor
        self.depression_factor *= (1.0 - effective_use)

        return self.weight * self.facilitation_factor * self.depression_factor

    def _apply_neuromodulation(self, base_weight: float,
                              neuromodulation: Dict[str, float]) -> float:
        """Apply neuromodulatory effects to synaptic transmission."""
        modulated_weight = base_weight

        # Dopamine modulation (affects plasticity and transmission)
        if "dopamine" in neuromodulation:
            dopamine = neuromodulation["dopamine"]
            if self.neurotransmitter == NeurotransmitterType.DOPAMINE:
                modulated_weight *= (1.0 + dopamine * 2.0)
            else:
                modulated_weight *= (1.0 + dopamine * 0.2)

        # Acetylcholine modulation (affects attention and learning)
        if "acetylcholine" in neuromodulation:
            ach = neuromodulation["acetylcholine"]
            modulated_weight *= (1.0 + ach * 0.3)

        # Norepinephrine modulation (affects arousal)
        if "norepinephrine" in neuromodulation:
            ne = neuromodulation["norepinephrine"]
            modulated_weight *= (1.0 + ne * 0.25)

        return modulated_weight

    def _update_synaptic_tag(self, signal_strength: float):
        """Update synaptic tag for capture mechanisms."""
        self.tag_strength *= self.tag_decay
        if signal_strength > self.capture_threshold:
            self.tag_strength += 0.1

    def apply_stdp(self, pre_trace: float, post_trace: float,
                   neuromodulation: Dict[str, float] = None) -> float:
        """
        Apply Spike-Timing Dependent Plasticity.

        Args:
            pre_trace: Pre-synaptic STDP trace
            post_trace: Post-synaptic STDP trace
            neuromodulation: Neuromodulator concentrations

        Returns:
            Weight change applied
        """
        if neuromodulation is None:
            neuromodulation = {}

        # Calculate STDP weight change
        delta_w = 0.0

        # Potentiation (post after pre)
        if post_trace > 0:
            delta_w += self.stdp_a_plus * pre_trace * post_trace

        # Depression (pre after post)
        if pre_trace > 0:
            delta_w -= self.stdp_a_minus * pre_trace * post_trace

        # Apply metaplasticity
        delta_w *= self.metaplasticity_factor

        # Modulate by neuromodulators
        if "dopamine" in neuromodulation:
            delta_w *= (1.0 + neuromodulation["dopamine"])

        if "acetylcholine" in neuromodulation:
            delta_w *= (1.0 + neuromodulation["acetylcholine"] * 0.5)

        # Apply synaptic tagging and capture
        if self.tag_strength > self.capture_threshold:
            delta_w *= (1.0 + self.tag_strength)

        # Update weight
        old_weight = self.weight
        self.update_weight(delta_w)

        # Update metaplasticity based on weight change
        self._update_metaplasticity(abs(delta_w))

        return self.weight - old_weight

    def _update_metaplasticity(self, weight_change: float):
        """Update metaplasticity factors based on recent activity."""
        self.activity_history.append(weight_change)

        # Keep only recent history
        if len(self.activity_history) > 100:
            self.activity_history.pop(0)

        # Calculate average recent activity
        avg_activity = sum(self.activity_history) / len(self.activity_history)

        # Adjust metaplasticity factor
        if avg_activity > self.metaplasticity_threshold:
            self.metaplasticity_factor *= 0.99  # Reduce plasticity
        else:
            self.metaplasticity_factor *= 1.001  # Increase plasticity

        # Keep within bounds
        self.metaplasticity_factor = max(0.1, min(2.0, self.metaplasticity_factor))

    def apply_homeostatic_scaling(self, target_activity: float, current_activity: float):
        """Apply homeostatic scaling to maintain target activity levels."""
        activity_error = target_activity - current_activity
        scaling_factor = 1.0 + activity_error * 0.001
        self.homeostatic_scaling *= scaling_factor
        self.homeostatic_scaling = max(0.1, min(2.0, self.homeostatic_scaling))

    def update_weight(self, delta_weight: float):
        """Update synaptic weight with constraints and homeostatic scaling."""
        # Apply homeostatic scaling
        scaled_delta = delta_weight * self.homeostatic_scaling

        # Update weight
        self.weight += scaled_delta

        # Apply constraints
        self.weight = max(self.min_weight, min(self.max_weight, self.weight))

        # Update structural strength based on weight
        self._update_structural_strength()

    def _update_structural_strength(self):
        """Update structural plasticity based on synaptic strength."""
        # Strengthen structure if weight is high
        if self.weight > 0.8 * self.max_weight:
            self.structural_strength += self.growth_factor

        # Weaken structure if weight is very low
        elif self.weight < self.pruning_threshold:
            self.structural_strength *= 0.999

        # Keep within bounds
        self.structural_strength = max(0.0, min(2.0, self.structural_strength))

    def should_be_pruned(self) -> bool:
        """Determine if this connection should be pruned."""
        return (self.structural_strength < 0.1 or
                self.weight < self.pruning_threshold or
                self.usage_trace < 0.01)

    def get_state_dict(self) -> Dict:
        """Get connection state for serialization."""
        return {
            'pre_neuron_id': self.pre_neuron_id,
            'post_neuron_id': self.post_neuron_id,
            'weight': self.weight,
            'synapse_type': self.synapse_type.value,
            'neurotransmitter': self.neurotransmitter.value,
            'structural_strength': self.structural_strength,
            'usage_trace': self.usage_trace,
            'metaplasticity_factor': self.metaplasticity_factor,
            'total_transmissions': self.total_transmissions
        }

    def __repr__(self):
        return (f"AdvancedConnection({self.pre_neuron_id} -> {self.post_neuron_id}, "
                f"w={self.weight:.3f}, type={self.synapse_type.value}, "
                f"nt={self.neurotransmitter.value}, struct={self.structural_strength:.2f})")


# Backward compatibility
class Connection(AdvancedConnection):
    """Backward compatible connection class."""
    def __init__(self, pre_neuron_id, post_neuron_id, weight=0.5, delay=1):
        super().__init__(pre_neuron_id, post_neuron_id, weight=weight, delay=delay)

    def update_weight(self, delta_weight):
        """Simple weight update for backward compatibility."""
        self.weight += delta_weight
        self.weight = max(0.0, min(1.0, self.weight))

