#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Enhanced training script for ASCE with improved performance."""

import sys
import os
import numpy as np
import time

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

def create_enhanced_dataset():
    """Create a more diverse dataset for better training."""
    texts = [
        # Basic sentences
        "the cat sits on the mat",
        "dogs run in the park",
        "birds fly in the sky",
        "fish swim in the water",
        "children play with toys",
        
        # AI and technology
        "artificial intelligence learns from data",
        "neural networks process information",
        "machine learning algorithms improve performance",
        "deep learning models recognize patterns",
        "computers solve complex problems",
        
        # Science and nature
        "the sun shines brightly today",
        "rain falls from dark clouds",
        "trees grow tall in forests",
        "flowers bloom in spring",
        "stars twinkle at night",
        
        # Simple conversations
        "hello how are you today",
        "good morning have a nice day",
        "thank you for your help",
        "please come back soon",
        "see you later goodbye",
        
        # Learning and education
        "students learn new concepts",
        "teachers explain difficult topics",
        "books contain valuable knowledge",
        "practice makes perfect",
        "education opens new opportunities",
        
        # Technology and future
        "robots help humans work",
        "smartphones connect people worldwide",
        "the internet provides information",
        "technology advances rapidly",
        "innovation drives progress"
    ]
    
    return texts

def train_enhanced_model():
    """Train ASCE with enhanced configuration."""
    print("=" * 70)
    print("ENHANCED ASCE TRAINING")
    print("=" * 70)
    
    try:
        from training.llm_trainer import ASCELanguageModel, TrainingConfig
        
        # Enhanced configuration
        config = TrainingConfig(
            vocab_size=500,
            sequence_length=32,
            embedding_dim=128,
            num_layers=4,
            batch_size=4,
            learning_rate=0.001,
            max_steps=100,
            save_interval=25,
            eval_interval=20
        )
        
        print("Creating Enhanced ASCE Model...")
        model = ASCELanguageModel(config)
        
        # Get enhanced dataset
        texts = create_enhanced_dataset()
        print(f"Dataset size: {len(texts)} texts")
        
        # Build vocabulary
        print("Building vocabulary...")
        model.data_processor.build_vocabulary(texts)
        vocab_size = len(model.data_processor.vocab)
        print(f"Vocabulary built: {vocab_size} tokens")
        
        # Split data
        split_idx = int(len(texts) * 0.8)
        train_texts = texts[:split_idx]
        eval_texts = texts[split_idx:]
        
        print(f"Training texts: {len(train_texts)}")
        print(f"Evaluation texts: {len(eval_texts)}")
        
        # Create training batches
        train_batches = model.data_processor.create_training_batches(
            train_texts, config.batch_size, config.sequence_length
        )
        print(f"Training batches: {len(train_batches)}")
        
        # Training loop with monitoring
        print("\nStarting Enhanced Training...")
        print("-" * 50)
        
        best_loss = float('inf')
        losses = []
        
        for step in range(config.max_steps):
            # Select random batch
            batch_idx = step % len(train_batches)
            inputs, targets = train_batches[batch_idx]
            
            # Training step
            step_result = model.train_step(inputs, targets)
            loss = step_result["loss"]
            losses.append(loss)
            
            # Log progress
            if step % 10 == 0:
                avg_loss = np.mean(losses[-10:]) if len(losses) >= 10 else loss
                lr = step_result["learning_rate"]
                
                print(f"Step {step:3d}: Loss={avg_loss:.4f}, LR={lr:.6f}")
                
                # Show system state
                curiosity = model.ms.get_curiosity_signal()
                neuromod = model.ms.get_neuromodulator_levels()
                print(f"         Curiosity={curiosity:.3f}, "
                      f"Dopamine={neuromod['dopamine']:.3f}")
            
            # Evaluation
            if step % config.eval_interval == 0 and step > 0:
                print("\nEvaluation:")
                eval_results = model.evaluate(eval_texts)
                eval_loss = eval_results["eval_loss"]
                perplexity = eval_results["perplexity"]
                print(f"  Eval Loss: {eval_loss:.4f}")
                print(f"  Perplexity: {perplexity:.2f}")
                
                # Test generation
                test_prompts = ["the cat", "artificial intelligence", "hello"]
                for prompt in test_prompts:
                    generated = model.generate_text(prompt, max_length=8, temperature=0.8)
                    print(f"  '{prompt}' -> '{generated}'")
                
                print()
            
            # Save best model
            if loss < best_loss:
                best_loss = loss
                model.save_model("enhanced_model.pkl")
        
        print("\nTraining completed!")
        print(f"Best loss: {best_loss:.4f}")
        
        # Final evaluation
        print("\n" + "=" * 50)
        print("FINAL EVALUATION")
        print("=" * 50)
        
        # Test various prompts
        test_prompts = [
            "the cat",
            "artificial intelligence",
            "hello how",
            "students learn",
            "technology advances",
            "the sun",
            "neural networks"
        ]
        
        print("Text Generation Results:")
        for prompt in test_prompts:
            # Generate with different temperatures
            for temp in [0.5, 1.0]:
                generated = model.generate_text(prompt, max_length=10, temperature=temp)
                print(f"  '{prompt}' (T={temp}) -> '{generated}'")
        
        # System statistics
        print("\nFinal System Statistics:")
        print("-" * 30)
        
        # Network statistics
        total_neurons = 0
        total_connections = 0
        total_activity = 0
        
        for i, cpu in enumerate(model.cpu_layers):
            stats = cpu.get_cpu_statistics()
            neurons = stats['network_stats']['total_neurons']
            connections = stats['network_stats']['total_connections']
            activity = stats['network_stats']['current_activity']
            
            total_neurons += neurons
            total_connections += connections
            total_activity += activity
            
            print(f"CPU Layer {i}: {neurons} neurons, {connections} connections, "
                  f"activity={activity:.3f}")
        
        print(f"Total: {total_neurons} neurons, {total_connections} connections")
        print(f"Average activity: {total_activity / len(model.cpu_layers):.3f}")
        
        # Memory system
        ams_stats = model.ams.get_memory_statistics()
        print(f"\nMemory System:")
        print(f"  Traces: {ams_stats['total_traces']}")
        print(f"  Usage: {ams_stats['memory_usage']:.2%}")
        print(f"  Avg strength: {ams_stats['average_strength']:.3f}")
        
        # Modulatory system
        ms_stats = model.ms.get_system_statistics()
        print(f"\nModulatory System:")
        print(f"  Curiosity: {ms_stats['motivation']['intrinsic_motivation']:.3f}")
        print(f"  Attention: {ms_stats['attention']['mode']}")
        print(f"  Learning rate: {model.ms.get_learning_modulation():.6f}")
        
        # Neuromodulators
        neuromod = model.ms.get_neuromodulator_levels()
        print(f"\nNeuromodulator Levels:")
        for nt, level in neuromod.items():
            print(f"  {nt.capitalize()}: {level:.3f}")
        
        print("\n" + "=" * 70)
        print("ENHANCED TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        
        return model
        
    except Exception as e:
        print(f"Enhanced training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def benchmark_performance(model):
    """Benchmark the model performance."""
    print("\n" + "=" * 50)
    print("PERFORMANCE BENCHMARK")
    print("=" * 50)
    
    if model is None:
        print("No model to benchmark")
        return
    
    # Speed test
    print("Speed Test:")
    test_input = "artificial intelligence"
    
    start_time = time.time()
    for i in range(10):
        generated = model.generate_text(test_input, max_length=5, temperature=1.0)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"  Average generation time: {avg_time:.3f} seconds")
    print(f"  Tokens per second: ~{5/avg_time:.1f}")
    
    # Quality test
    print("\nQuality Test:")
    quality_prompts = [
        "the quick brown",
        "machine learning is",
        "in the future",
        "scientists discovered",
        "children love to"
    ]
    
    for prompt in quality_prompts:
        generated = model.generate_text(prompt, max_length=8, temperature=0.7)
        print(f"  '{prompt}' -> '{generated}'")
    
    # Consistency test
    print("\nConsistency Test (same prompt, different runs):")
    test_prompt = "neural networks"
    for i in range(5):
        generated = model.generate_text(test_prompt, max_length=6, temperature=0.8)
        print(f"  Run {i+1}: '{generated}'")

if __name__ == "__main__":
    print("Starting Enhanced ASCE Training and Evaluation...")
    
    # Train enhanced model
    model = train_enhanced_model()
    
    # Benchmark performance
    if model:
        benchmark_performance(model)
    
    print("\nAll tests completed!")
