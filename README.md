# ASCE Prototype - Adaptive Sparse Cognitive Engine (Simplified)

This project provides a simplified Python prototype demonstrating some core concepts
inspired by the theoretical Adaptive Sparse Cognitive Engine (ASCE) research plan.

**Goal:** To illustrate principles like sparse networks, event-driven computation,
local plasticity, and basic modularity (CPU, AMS, MS) in code, rather than
creating a fully functional, high-performance AI.

**Disclaimer:** This is a highly simplified proof-of-concept and does not represent
the full complexity or potential capabilities envisioned in the ASCE research plan.
Many features (e.g., sophisticated abstraction, complex forgetting, structural
plasticity) are omitted for clarity.

## Project Structure

```
asce_prototype/

  ├── main.py             # Main simulation runner and entry point
  |
  ├── core/               # Foundational neural network elements
  │   ├── __init__.py
  │   ├── neuron.py         # Defines the basic Neuron class
  │   ├── connection.py     # Defines the Connection class between neurons
  │   ├── sparse_network.py # Manages sparse connectivity and network stepping
  │   └── plasticity.py     # Implements simple Event-Driven Synaptic Plasticity (EDSP) rules
  |
  ├── components/         # Higher-level ASCE architectural modules
  │   ├── __init__.py
  │   ├── cpu.py            # Cognitive Processing Unit (contains a SparseNetwork)
  │   ├── ams.py            # Simplified Abstract Memory System (stores/retrieves patterns)
  │   └── ms.py             # Simplified Modulatory System (generates context/learning signals)
  |
  ├── environment/        # Simulation tasks and environments
  │   ├── __init__.py
  │   └── toy_task.py       # Defines the SimpleContextualAssociationTask
  |
  ├── utils/              # Helper utilities (currently empty/basic)
  │   └── __init__.py
  # │   └── visualization.py # (Plotting is currently done directly in main.py)
  |
  └── README.md           # This file
```

## Dependencies

*   Python 3.x
*   NumPy (`pip install numpy`)
*   Matplotlib (`pip install matplotlib`) - Optional, for visualizing training results.

## Usage

1.  Navigate to the `asce_prototype` directory in your terminal.
2.  Run the main simulation script:
    ```bash
    python main.py
    ```

This will:
*   Set up a simple ASCE instance with one CPU, AMS, and MS.
*   Configure the `SimpleContextualAssociationTask`.
*   Run a training loop for a set number of trials, simulating the ASCE processing the task.
*   Apply simple Hebbian-like event-driven plasticity modulated by a basic curiosity/error signal from the MS.
*   Print progress updates to the console.
*   If Matplotlib is installed, generate a plot (`asce_training_plot.png`) showing rolling accuracy, error signal, and learning rate modulation over the training trials.

## Key Modules Explained

*   **`core/sparse_network.py`**: Implements the network using dictionaries to store connections, ensuring sparsity. The `step()` method simulates one tick, propagating signals only from neurons that fired in the previous tick and applying plasticity locally.
*   **`core/plasticity.py`**: Contains `apply_simple_hebbian_edsp`, which modifies connection weights only between neurons that were recently co-active, demonstrating event-driven learning.
*   **`components/cpu.py`**: Wraps a `SparseNetwork` and defines input/output interfaces.
*   **`components/ams.py`**: Provides basic pattern storage and retrieval based on similarity (cosine similarity used here).
*   **`components/ms.py`**: Generates placeholder signals for context and learning rate modulation (influenced by simulated error/novelty).
*   **`environment/toy_task.py`**: Defines a task where input patterns (`A`, `B`) map to different output patterns (`X`, `Y`) depending on the current context (0 or 1). Provides methods to get trials and evaluate the network's response.
*   **`main.py`**: Orchestrates the setup, training loop, interaction between components (CPU step, MS update, task evaluation), and visualization.

## Limitations & Future Work

*   **Simplistic Plasticity:** Uses a very basic Hebbian rule. Real EDSP would likely involve more complex timing-dependent rules (STDP) and more nuanced modulation.
*   **Basic AMS:** The AMS only stores raw output patterns and uses simple similarity. A true AMS would involve significant compression, abstraction, and consolidation.
*   **Rudimentary MS:** Curiosity and context signals are placeholders. A real MS would involve more complex state tracking and signal generation.
*   **No Active Forgetting:** The AFA algorithm is not implemented.
*   **Limited Scalability:** The current implementation is for demonstration and not optimized for large-scale networks.
*   **Toy Task:** The task is very simple and doesn't test complex reasoning or generalization.

This prototype serves as a starting point for exploring ASCE concepts. Further development would involve refining the algorithms, implementing missing features like active forgetting, developing more complex tasks, and optimizing the code for performance.

