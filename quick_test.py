#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Quick test script for ASCE components."""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

def test_core_components():
    """Test core neural components."""
    print("Testing Core Components...")
    
    # Test AdvancedNeuron
    from core.neuron import AdvancedNeuron
    neuron = AdvancedNeuron(neuron_id=0, neuron_type="excitatory")
    neuron.integrate_input(0.8)
    neuron.update_potential()
    print(f"✓ AdvancedNeuron: {neuron}")
    
    # Test AdvancedConnection
    from core.connection import AdvancedConnection, SynapseType, NeurotransmitterType
    connection = AdvancedConnection(
        pre_neuron_id=0, 
        post_neuron_id=1,
        synapse_type=SynapseType.EXCITATORY,
        neurotransmitter=NeurotransmitterType.GLUTAMATE
    )
    print(f"✓ AdvancedConnection: {connection}")
    
    # Test AdvancedSparseNetwork
    from core.sparse_network import AdvancedSparseNetwork
    network = AdvancedSparseNetwork()
    n1 = network.add_neuron(neuron_type="excitatory")
    n2 = network.add_neuron(neuron_type="excitatory")
    network.add_connection(n1, n2, weight=0.5)
    network.stimulate_neuron(n1, 1.0)
    step_result = network.step()
    print(f"✓ AdvancedSparseNetwork: {network}")
    
    print("Core components test completed!\n")

def test_cognitive_components():
    """Test cognitive architecture components."""
    print("Testing Cognitive Components...")
    
    # Test AdvancedCPU
    from components.cpu import AdvancedCPU
    cpu_config = {
        "input_size": 32,
        "output_size": 16,
        "hidden_layers": [64, 32],
        "sparsity_level": 0.2
    }
    cpu = AdvancedCPU(cpu_id=0, architecture_config=cpu_config)
    
    # Test input processing
    test_input = np.random.rand(32)
    cpu.apply_input(test_input, input_type="vector")
    step_result = cpu.step()
    output = cpu.get_output(output_format="vector")
    print(f"✓ AdvancedCPU: {cpu}")
    print(f"  Input shape: {test_input.shape}, Output shape: {output.shape}")
    
    # Test AdvancedAMS
    from components.ams import AdvancedAMS, MemoryType
    ams = AdvancedAMS(ams_id=0, representation_size=64)
    
    # Store some patterns
    pattern1 = np.random.rand(64)
    pattern2 = np.random.rand(64)
    
    trace_id1 = ams.store_pattern(pattern1, MemoryType.EPISODIC, {"context": "test1"})
    trace_id2 = ams.store_pattern(pattern2, MemoryType.SEMANTIC, {"context": "test2"})
    
    # Retrieve similar patterns
    query_pattern = pattern1 + np.random.normal(0, 0.1, 64)
    results = ams.retrieve_similar(query_pattern, top_k=2)
    
    print(f"✓ AdvancedAMS: {ams}")
    print(f"  Stored {len(ams.memory_traces)} patterns, retrieved {len(results)} similar")
    
    # Test AdvancedModulatorySystem
    from components.ms import AdvancedModulatorySystem
    ms = AdvancedModulatorySystem(ms_id=0)
    
    # Update with feedback
    system_feedback = {
        "prediction_error": 0.3,
        "task_success": True,
        "novelty_level": 0.5
    }
    external_signals = {
        "reward": 0.8,
        "stress": 0.2
    }
    
    neuromod_levels = ms.update_state(system_feedback, external_signals)
    print(f"✓ AdvancedModulatorySystem: {ms}")
    print(f"  Neuromodulator levels: {list(neuromod_levels.keys())}")
    
    print("Cognitive components test completed!\n")

def test_language_model():
    """Test language model components."""
    print("Testing Language Model...")
    
    try:
        from training.llm_trainer import ASCELanguageModel, TrainingConfig
        
        # Create small config for testing
        config = TrainingConfig(
            vocab_size=1000,
            sequence_length=32,
            embedding_dim=128,
            num_layers=2,
            batch_size=2,
            max_steps=10
        )
        
        # Create model
        model = ASCELanguageModel(config)
        print(f"✓ ASCELanguageModel created with {config.num_layers} layers")
        
        # Test text processing
        sample_texts = [
            "The quick brown fox jumps over the lazy dog.",
            "Artificial intelligence is transforming the world.",
            "Neural networks learn complex patterns from data."
        ]
        
        # Build vocabulary
        model.data_processor.build_vocabulary(sample_texts)
        print(f"✓ Vocabulary built with {len(model.data_processor.vocab)} tokens")
        
        # Test encoding/decoding
        text = sample_texts[0]
        encoded = model.data_processor.encode_text(text, max_length=16)
        decoded = model.data_processor.decode_tokens(encoded)
        print(f"✓ Text encoding/decoding: '{text[:20]}...' -> {len(encoded)} tokens")
        
        # Test forward pass
        input_batch = np.array([encoded[:config.sequence_length], encoded[:config.sequence_length]])
        logits = model.forward(input_batch, training=False)
        print(f"✓ Forward pass: input {input_batch.shape} -> logits {logits.shape}")
        
        # Test generation
        generated = model.generate_text("The quick", max_length=10, temperature=1.0)
        print(f"✓ Text generation: 'The quick' -> '{generated}'")
        
        print("Language model test completed!\n")
        
    except Exception as e:
        print(f"✗ Language model test failed: {e}")
        print("This is expected if training dependencies are missing.\n")

def test_integration():
    """Test component integration."""
    print("Testing Component Integration...")
    
    # Test CPU-AMS interaction
    from components.cpu import AdvancedCPU
    from components.ams import AdvancedAMS, MemoryType
    from components.ms import AdvancedModulatorySystem
    
    # Create components
    cpu = AdvancedCPU(cpu_id=0, architecture_config={"input_size": 16, "output_size": 8, "hidden_layers": [16]})
    ams = AdvancedAMS(ams_id=0, representation_size=8)
    ms = AdvancedModulatorySystem(ms_id=0)
    
    # Simulate processing loop
    for step in range(5):
        # Generate input
        input_data = np.random.rand(16)
        
        # Process through CPU
        cpu.apply_input(input_data)
        cpu_result = cpu.step(modulatory_signals=ms.get_neuromodulator_levels())
        
        # Get output and store in AMS
        output = cpu.get_output(output_format="vector")
        if np.any(output > 0.5):  # Only store if significant activity
            ams.store_pattern(output, MemoryType.WORKING, {"step": step})
        
        # Update modulatory system
        system_feedback = {
            "prediction_error": np.random.rand() * 0.5,
            "task_success": np.random.rand() > 0.5,
            "novelty_level": np.random.rand()
        }
        ms.update_state(system_feedback)
    
    print(f"✓ Integration test: {len(ams.memory_traces)} patterns stored over 5 steps")
    print(f"  Final curiosity level: {ms.get_curiosity_signal():.3f}")
    print(f"  CPU activity: {cpu.get_cpu_statistics()['network_stats']['current_activity']:.3f}")
    
    print("Integration test completed!\n")

def main():
    """Run all tests."""
    print("=" * 60)
    print("ASCE QUICK TEST SUITE")
    print("=" * 60)
    
    try:
        test_core_components()
        test_cognitive_components()
        test_language_model()
        test_integration()
        
        print("=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY! ✓")
        print("=" * 60)
        print("\nYou can now run:")
        print("  python advanced_main.py --mode demo")
        print("  python main.py  # for original prototype")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
