# -*- coding: utf-8 -*-

"""Main entry point and simulation runner for the ASCE prototype."""

import random
import time
import numpy as np

from core.neuron import <PERSON>euron
from core.connection import Connection
from core.sparse_network import SparseNetwork
from core.plasticity import PLASTICITY_RULES

from components.cpu import CPU
from components.ams import AMS
from components.ms import ModulatorySystem

from environment.toy_task import SimpleContextualAssociationTask

# --- Simulation Setup --- #

def setup_asce_for_task(task):
    """Creates an ASCE instance configured for the given task."""
    # 1. Modulatory System
    ms = ModulatorySystem(ms_id=0)

    # 2. Cognitive Processing Unit - size based on task
    cpu = CPU(cpu_id=0, plasticity_rule="simple_hebbian")
    input_ids = [cpu.add_neuron(is_input=True, threshold=0.6) for _ in range(task.num_input)]
    output_ids = [cpu.add_neuron(is_output=True, threshold=0.6) for _ in range(task.num_output)]
    # Add some hidden neurons
    num_hidden = 5
    hidden_ids = [cpu.add_neuron(threshold=0.5) for _ in range(num_hidden)]

    # Add sparse connections (more structured than pure random)
    # Input -> Hidden
    for in_id in input_ids:
        for h_id in random.sample(hidden_ids, k=min(3, num_hidden)): # Connect each input to a few hidden
             cpu.add_connection(in_id, h_id, weight=random.uniform(0.3, 0.7))
    # Hidden -> Hidden (sparse recurrent)
    for _ in range(num_hidden * 2):
        pre_h = random.choice(hidden_ids)
        post_h = random.choice(hidden_ids)
        if pre_h != post_h:
             try:
                 cpu.add_connection(pre_h, post_h, weight=random.uniform(0.1, 0.5))
             except ValueError:
                 pass
    # Hidden -> Output
    for h_id in hidden_ids:
        for out_id in random.sample(output_ids, k=min(2, task.num_output)): # Connect each hidden to a few output
             cpu.add_connection(h_id, out_id, weight=random.uniform(0.3, 0.7))

    # 3. Abstract Memory System
    ams_rep_size = task.num_output # AMS stores patterns related to output
    ams = AMS(ams_id=0, representation_size=ams_rep_size)

    print("ASCE Setup Complete for Task:")
    print(ms)
    print(cpu)
    print(ams)
    print("----------------------------------")
    return ms, cpu, ams

def run_training_loop(ms, cpu, ams, task, total_trials=200, context_switch_freq=50, ticks_per_trial=10):
    """Runs the training loop on the specified task."""
    print(f"\n--- Starting Training ({total_trials} trials) ---")
    results = {"trial": [], "context": [], "input": [], "target": [], "output": [], "correct": [], "error": [], "lr_mod": []}

    start_time = time.time()
    correct_count = 0

    for trial in range(total_trials):
        # --- Get Trial Info --- #
        input_key, input_vector, current_context = task.get_trial()
        target_key = task.get_correct_output_key(input_key, current_context)
        target_vector = task.get_correct_output_vector(input_key, current_context)

        # --- Present Input & Run Network --- #
        # Reset potential before applying new input for clarity in simple tasks
        for neuron in cpu.network.neurons.values():
            neuron.potential = 0.0
            neuron.fired_this_tick = False
            neuron.inputs_this_tick = 0.0
            neuron.refractory_period = 0

        # Apply input vector (map to input neuron IDs)
        input_dict = {cpu.input_neuron_ids.copy().pop(): val for i, val in enumerate(input_vector) if i < len(cpu.input_neuron_ids)}
        # A bit hacky way to map list to set, assumes order preservation somewhat
        # Better: map explicitly during setup if needed
        input_dict = {list(cpu.input_neuron_ids)[i]: val for i, val in enumerate(input_vector)}

        cpu.apply_input(input_dict)

        # Run network for a few ticks to let activity propagate
        final_fired_ids = set()
        for tick in range(ticks_per_trial):
            # Update MS based on *previous* trial error (or use other signals)
            # For simplicity, we use a decaying novelty/error signal here
            simulated_error = results["error"][-1] if results["error"] else 0.5 # Use last error
            ms.update_state(system_error=simulated_error, external_context_cue=current_context)
            learning_mod = ms.get_learning_modulation()

            # Step the CPU
            fired_ids = cpu.step(modulatory_signal=learning_mod)
            if tick == ticks_per_trial - 1: # Capture output on last tick
                final_fired_ids = fired_ids

        # --- Evaluate Output --- #
        network_output_dict = cpu.get_output()
        # Convert output dict {neuron_id: fired_bool} to vector based on sorted output IDs
        sorted_output_ids = sorted(list(cpu.output_neuron_ids))
        network_output_vector = [1.0 if network_output_dict.get(nid, False) else 0.0 for nid in sorted_output_ids]

        is_correct, error_signal, _ = task.evaluate_response(input_key, current_context, network_output_vector)
        if is_correct:
            correct_count += 1

        # --- Log Results --- #
        results["trial"].append(trial)
        results["context"].append(current_context)
        results["input"].append(input_key)
        results["target"].append(target_key)
        results["output"].append(np.argmax(network_output_vector) if sum(network_output_vector) > 0 else -1) # Store index of fired output neuron
        results["correct"].append(is_correct)
        results["error"].append(error_signal)
        results["lr_mod"].append(ms.get_learning_modulation())

        # --- Optional: AMS Interaction --- #
        # if error_signal > 0.5: # Example: Store surprising/incorrect outcomes
        #     ams.store_pattern(network_output_vector, metadata={\"trial\": trial, \"context\": current_context, \"input\": input_key})

        # --- Context Switch --- #
        if (trial + 1) % context_switch_freq == 0 and trial < total_trials - 1:
            task.switch_context()
            print("---")

        # --- Print Progress --- #
        if (trial + 1) % 20 == 0:
            accuracy = correct_count / 20.0
            print(f"Trial {trial+1}/{total_trials} | Context: {current_context} | Input: {input_key} -> Target: {target_key} | Output Neuron: {results['output'][-1]} | Correct: {is_correct} | Error: {error_signal:.2f} | LRMod: {results['lr_mod'][-1]:.4f} | Recent Acc: {accuracy:.2f}")
            correct_count = 0 # Reset count for next block

    end_time = time.time()
    print(f"--- Training Finished ({end_time - start_time:.2f} seconds) ---")
    total_accuracy = sum(results["correct"]) / total_trials
    print(f"Overall Accuracy: {total_accuracy:.3f}")

    return results

# --- Visualization (Simple Example) --- #
def plot_results(results, context_switch_freq):
    try:
        import matplotlib.pyplot as plt
    except ImportError:
        print("Matplotlib not found. Skipping plotting. Install with: pip install matplotlib")
        return

    trials = results["trial"]
    accuracy = [1 if c else 0 for c in results["correct"]]
    error = results["error"]
    lr_mod = results["lr_mod"]
    contexts = results["context"]

    # Calculate rolling accuracy
    window_size = 20
    rolling_acc = np.convolve(accuracy, np.ones(window_size)/window_size, mode="valid")

    fig, axs = plt.subplots(3, 1, figsize=(10, 8), sharex=True)

    # Accuracy Plot
    axs[0].plot(trials[window_size-1:], rolling_acc, label=f"Rolling Accuracy (w={window_size})")
    # Mark context switches
    for i in range(len(trials)):
        if i > 0 and contexts[i] != contexts[i-1]:
            axs[0].axvline(trials[i], color="r", linestyle="--", alpha=0.5, label="Context Switch" if i == context_switch_freq else None)
    axs[0].set_ylabel("Accuracy")
    axs[0].set_ylim(0, 1.1)
    axs[0].set_title("ASCE Prototype Training Performance")
    axs[0].grid(True)
    axs[0].legend()

    # Error Plot
    axs[1].plot(trials, error, label="Error Signal", alpha=0.7)
    axs[1].set_ylabel("Error Signal")
    axs[1].grid(True)
    axs[1].legend()

    # Learning Rate Modulation Plot
    axs[2].plot(trials, lr_mod, label="Learning Rate Mod")
    axs[2].set_xlabel("Trial Number")
    axs[2].set_ylabel("LR Modulation")
    axs[2].grid(True)
    axs[2].legend()

    plt.tight_layout()
    plt.savefig("asce_training_plot.png")
    print("\nSaved training plot to asce_training_plot.png")
    # plt.show() # Use savefig in non-interactive environments

if __name__ == "__main__":
    # 1. Setup Task
    task_instance = SimpleContextualAssociationTask(num_input_neurons=2, num_output_neurons=2)

    # 2. Setup ASCE Model
    ms_instance, cpu_instance, ams_instance = setup_asce_for_task(task_instance)

    # Define training parameters
    total_trials = 200
    context_switch_freq = 50
    ticks_per_trial = 5

    # 3. Run Training
    training_results = run_training_loop(ms_instance, cpu_instance, ams_instance, task_instance,
                                         total_trials=total_trials, context_switch_freq=context_switch_freq, ticks_per_trial=ticks_per_trial)

    # 4. Visualize Results
    plot_results(training_results, context_switch_freq=context_switch_freq)

